/* eslint-disable max-lines */
import { CaretRightOutlined, FolderOutlined, VerticalAlignBottomOutlined, VerticalAlignTopOutlined } from '@ant-design/icons';
import { Breadcrumb, Button, Collapse, Dropdown, Empty, MenuProps, Tooltip, message } from '@gwy/components-web';
// import {  } from 'antd';
import IconFormApprove from '@/assets/app-form-manage/icon-form-approve.png';
import IconFormExcute from '@/assets/app-form-manage/icon-form-execution.png';
import { formManageApi } from '@/services';
import classNames from 'classnames';
import { isEmpty } from 'lodash';
import { get } from 'lodash-es';
import { useEffect, useMemo, useState } from 'react';
import styles from './index.module.less';

export interface ApplyListCardsProps {
  bridge: any;
  folderAppList: any[];
  handleAppClick: (val: any) => void;
  baseInfo: any;
  setBaseInfo: (val: any) => void;
  refreshAppList?: () => void;
}

const ApplyListCards = ({ folderAppList, handleAppClick, baseInfo, refreshAppList }: ApplyListCardsProps) => {
  const [expandPostKeys, setExpandPostKeys] = useState([]);
  const [dropVisable, setDropVisable] = useState<boolean>(false);
  const [clickFieldId, setClickFieldId] = useState(null);
  const [folderMoveModal, setFolderMoveModal] = useState<{
    visible: boolean;
    fromFolderId: string;
    appId: string;
  }>({
    visible: false,
    fromFolderId: '',
    appId: '',
  });
  console.log('baseInfo', baseInfo);
  const [createFolderOpen, setCreateFolderOpen] = useState(false);
  const [breadcrumItems, setBreadcrumItems] = useState<any[]>([
    {
      folderId: 'index',
      folderName: '主页',
    },
  ]);
  const navigateToFolder = (item) => () => {
    setBreadcrumItems((pre) => {
      const index = pre.findIndex((i) => i.folderId === item.folderId);
      return pre.slice(0, index + 1);
    });
    if (item?.folderId === 'index') {
      // onFolderFilter({
      //   parentId: '',
      //   appList: [],
      // });
      // fetchFolderAppList();
    } else {
      // onFolderFilter({
      //   parentId: item?.folderId,
      //   appList: [],
      // });
    }
  };
  const handleTopForm = async (item) => {
    const { formVersionId } = item;
    await formManageApi.updateFormTop({
      formVersionId,
      topType: 'FORM_APP_SORT',
    });
    message.success('操作成功');
    // fetchFolderAppList(baseInfo?.postId);
    refreshAppList();
  };

  const handleCancelTopForm = async (item) => {
    const { formVersionId } = item;
    await formManageApi.updateFormCancelTop({
      formVersionId,
      topType: 'FORM_APP_SORT',
    });
    message.success('操作成功');
    // fetchFolderAppList(baseInfo?.postId);
    refreshAppList();
  };
  const items = (item): MenuProps['items'] => {
    // const isStash = item.status === MetaAppStatus.Stash;
    const isTop = item.ifTop;
    return [
      !isTop && {
        key: 'top',
        label: (
          <a
            onClick={(e) => {
              setDropVisable(false);
              e.stopPropagation();
              handleTopForm(item);
            }}
          >
            置顶
          </a>
        ),
        icon: <VerticalAlignTopOutlined />,
      },
      isTop && {
        key: 'cancelTop',
        label: (
          <a
            onClick={(e) => {
              setDropVisable(false);
              e.stopPropagation();
              handleCancelTopForm(item);
            }}
          >
            取消置顶
          </a>
        ),
        icon: <VerticalAlignBottomOutlined />,
      },
    ].filter(Boolean);
  };
  const findNodeByFolderId = (tree, folderId) => {
    const findNode = (node) => (node.folderId === folderId ? node : node.childList?.map(findNode).find((child) => child) || null);
    return findNode(tree);
  };

  const currentFoldObj = useMemo(() => {
    // 遍历集团的每个岗位，一个岗位对应一颗树，递归树查找节点
    const appFolderByPosts = get(folderAppList, [0, 'children']) || [];
    let node = null;
    for (let index = 0; index < appFolderByPosts.length; index++) {
      const element = appFolderByPosts[index] || null;
      const appFolderVO = element?.appFolderVO || null;
      node = findNodeByFolderId({ ...appFolderVO, folderId: 'index' }, breadcrumItems[breadcrumItems.length - 1].folderId);
      if (!isEmpty(node)) break;
    }
    return node?.folderId === 'index' ? null : node;
  }, [breadcrumItems, folderAppList]);

  const renderBreadcrumb = useMemo(() => {
    if (breadcrumItems.length <= 1) return null;
    return (
      <Breadcrumb style={{ marginBottom: 16, padding: '0 20px' }} className={styles.breadcrumbWrapper}>
        {breadcrumItems &&
          breadcrumItems.map((item, index) => {
            if (index === breadcrumItems.length - 1) {
              return <Breadcrumb.Item key={item.folderId}>{item.folderName}</Breadcrumb.Item>;
            }
            return (
              <Breadcrumb.Item onClick={navigateToFolder(item)} href="#" key={item.folderId}>
                {item.folderName}
              </Breadcrumb.Item>
            );
          })}
      </Breadcrumb>
    );
  }, [breadcrumItems]);
  const generateMenuItems = (itemInfo): MenuProps['items'] => {
    return [
      {
        label: (
          <>
            <Button
              type="text"
              onClick={() => {
                setCreateFolderOpen(false);
                // createNewFolder();
              }}
              icon={<FolderOutlined />}
            >
              新建文件夹
            </Button>
          </>
        ),
        key: '_create-folder_',
      },
    ];
  };

  useEffect(() => {
    const handleIds = (OrgList) => {
      return OrgList.reduce((prev, cur) => {
        const tempArr = [];
        const targetId = cur?.postId || cur?.orgId || cur?.blocId;
        tempArr.push(targetId);
        if (cur?.children?.length) {
          tempArr.push(...handleIds(cur?.children || []));
        }
        // 默认展开个人表单、游客表单
        if (cur.appFolderVO) {
          tempArr.push('personalForm', ...handleIds(cur.appFolderVO.orgs || []));
        }

        return prev.concat(tempArr);
      }, []).filter(Boolean);
    };
    let expandPostKeys = handleIds(folderAppList);

    expandPostKeys = [baseInfo.postId];
    setExpandPostKeys(expandPostKeys);
  }, [folderAppList]);

  const renderCardItem = (appFolderVO, inFolder?) => {
    let renderList = appFolderVO;
    if (isEmpty(renderList)) {
      return <Empty style={{ height: '60%', minHeight: '186px' }} description={'暂无表单'} />;
    }

    return (
      <div style={{ position: 'relative' }}>
        <div className={styles.sortBtn} style={{ top: inFolder ? '-42px' : '-35px' }}></div>
        <div className={classNames(styles.listWrapper)}>
          {renderList?.map((item) => {
            const isExcuteForm = item?.appType === 'SUBMIT';
            return (
              <div
                className={classNames(styles.cardWrapper)}
                key={item?.formId}
                onDoubleClick={() => {
                  handleAppClick(item);
                }}
                draggable={false}
                onDragStart={(e) => {
                  e.dataTransfer.setData('text', item?.formId);
                }}
                onDragOver={(e) => {
                  e.preventDefault();
                }}
              >
                <img
                  src={isExcuteForm ? IconFormExcute : IconFormApprove}
                  className={styles.fileImg}
                  style={{
                    width: '50px',
                    height: '56px',
                  }}
                />
                {/* <img src={`${CDN_URL_DATASOURCE}/form-manage/form.png`} className={styles.fileImg} /> */}
                <div className={styles.titleWrap}>
                  <div style={{ display: 'flex', alignItems: 'center', overflow: 'hidden' }}>
                    <Tooltip title={item?.name || item?.appName || '-'}>
                      <div
                        className={styles.title}
                        style={{
                          maxWidth: '100%',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                        }}
                      >
                        {item?.name || item?.appName || '-'}
                      </div>
                    </Tooltip>
                  </div>
                </div>
                {item.ifTop && (
                  <div
                    style={{
                      position: 'absolute',
                      right: 44,
                      bottom: 50,
                      fontSize: 10,
                      color: '#4c7bf6',
                    }}
                  >
                    顶
                  </div>
                )}
                <div
                  className={styles.actions}
                  style={{
                    position: 'absolute',
                    top: '10px',
                    right: '10px',
                  }}
                >
                  {items(item)?.length > 0 && (
                    <Dropdown
                      menu={{ items: items(item) }}
                      placement="bottomCenter"
                      open={dropVisable && item?.formVersionId === clickFieldId}
                      onOpenChange={() => setDropVisable(!dropVisable)}
                      trigger={['click']}
                    >
                      <Button
                        type="text"
                        size="small"
                        style={{ fontSize: '12px', lineHeight: '0', padding: 0 }}
                        onClick={(e) => {
                          // setDropVisable(true);
                          e.stopPropagation();
                          setClickFieldId(item?.formVersionId);
                          setDropVisable(true);
                        }}
                      >
                        ···
                      </Button>
                    </Dropdown>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const renderOrgAndPost = (orgList, rank) => {
    return (
      <div>
        {!!orgList &&
          !!orgList?.length &&
          orgList.map((item) => {
            const { children, title } = item;

            return (
              <div
                key={`${item.postId || item.orgId}_link`}
                id={`${item.postId || item.orgId}_link`}
                onClick={(e) => {
                  e.stopPropagation();
                }}
                onContextMenu={(e) => {
                  // e.stopPropagation();
                }}
              >
                <Collapse
                  ghost
                  activeKey={expandPostKeys}
                  expandIcon={({ isActive }) => <CaretRightOutlined rotate={isActive ? 90 : 0} />}
                  onChange={(keys: any) => setExpandPostKeys(keys)}
                >
                  <Collapse.Panel
                    key={item.postId || item.orgId || item?.blocId}
                    header={
                      <>
                        <span
                          style={{
                            fontWeight: 'bold',
                          }}
                        >
                          {title}
                        </span>
                        {item?.dataList.length === 0 && <span style={{ color: '#4e5969' }}>（暂无表单）</span>}
                      </>
                    }
                  >
                    {item?.dataList.length ? renderCardItem(item?.dataList) : null}
                    {!!children && !!children?.length && renderOrgAndPost(children, rank + 1)}
                  </Collapse.Panel>
                </Collapse>
              </div>
            );
          })}
      </div>
    );
  };

  return (
    <>
      {folderAppList.length > 0 ? (
        <div style={{ overflow: 'auto', height: 'calc(100% - 50px)', padding: '0 20px' }}>
          {renderBreadcrumb}
          <Dropdown
            menu={{ items: generateMenuItems(folderAppList) }}
            trigger={['contextMenu']}
            open={false}
            onOpenChange={(open) => {
              setCreateFolderOpen(open);
            }}
          >
            {renderOrgAndPost(
              [
                {
                  postId: baseInfo?.selectedPost.postId,
                  title: `${baseInfo?.selectedPost?.departmentName}-${baseInfo?.selectedPost?.postName}`,
                  dataList: currentFoldObj ? currentFoldObj : folderAppList,
                },
              ],
              1,
            )}
          </Dropdown>
        </div>
      ) : (
        <div
          style={{
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Empty description={'暂无表单'} />
        </div>
        // <Empty style={{ height: '60%', minHeight: '500px' }} description={'暂无表单'} />
      )}
    </>
  );
};

export default ApplyListCards;
