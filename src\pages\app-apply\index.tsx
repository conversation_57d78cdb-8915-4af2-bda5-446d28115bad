import DataQuerySetting from '@/components/data-query-setting';
import { FILTER_VALUE_TYPE } from '@/components/filter-group/filter-const';
import { useRoute } from '@/hooks';
import { getFormVersion } from '@/services/app';
import { getOrgDataUnitsTags } from '@/services/org-data-unit';
import { FormAppVO } from '@/types/app';
import { Post } from '@/types/post';
import { DataUnitTagVO } from '@/types/tag';
import { genOpenMicroApp } from '@/utils';
import { getMetaTagUniqueId } from '@/utils/metadata';
import { isEmpty } from 'lodash-es';
import { useEffect, useMemo, useState } from 'react';
import { AppType } from '../app-designer/const';
import AppForm from './components/app-form';
import AppTable from './components/app-table';

export type AppFormProps = {
  post: Post;
  app: FormAppVO;
  isPreview?: boolean;
  dataId?: string;
  isReturn?: boolean;
  bizId?: string;
  bizType?: number;
  [key: string]: any;
};

export const openAppApply = genOpenMicroApp<AppFormProps>('app-apply');

const AppApply = () => {
  const route = useRoute();

  const {
    post,
    app,
    isPreview,
    dataId,
    isReturn,
    bizId,
    bizType,
    allDataIds,
    isSameGroup,
    isSamePiece,
    filters: tableFilters,
    dataGroupOption,
  } = ((route as any).state || {}) as AppFormProps;
  console.log(dataGroupOption, 'dataGroupOption-----');
  const [filterOpen, setFilterOpen] = useState(false);
  const [formVersion, setFormVersion] = useState<any>({});
  // 标签映射
  const [cTagsMap, setCTagsMap] = useState<Record<number, DataUnitTagVO>>({});
  const [finishCfgParams, setFinishCfgParams] = useState(false);
  const [originalFilters, setOriginalFilters] = useState(null); // 接口返参
  const [filters, setFilters] = useState(null); // 用于筛选的查询条件，因为动态入参可以在预览模式更改筛选条件，需要用户手动更改
  const [filterUseTag, setFilterUseTag] = useState([]); // 筛选条件使用的标签
  const needCheck = app.appType === AppType.Approve;

  const fetchFormVersion = async (formVersionId) => {
    const formVersion = await getFormVersion(formVersionId);
    setFormVersion(formVersion);
    const mainDataUnitInfo = formVersion.dataUnits.find((item) => item.mainDataUnit);
    setOriginalFilters({
      ...(mainDataUnitInfo || {}),
    });
    setFilters({
      ...(mainDataUnitInfo || {}),
    });
    fetchDataUnitsWithTags(formVersion.dataUnits.map((item) => item.dataUnitId));
    return formVersion;
  };
  const fetchDataUnitsWithTags = async (dataUnitIds) => {
    const dataUnits = await getOrgDataUnitsTags({ orgId: post?.orgId, dataUnitIds });
    const handleDataUnitToTags = (dataUnitsOps) => {
      let results = [];
      dataUnitsOps.forEach((dataUnit) => {
        if (dataUnit?.tagList?.length > 0) {
          results.push(
            ...(dataUnit.tagList || []).map((tag) => ({
              ...tag,
              belongDataUnitId: dataUnit.dataUnitId,
              belongDataUnitName: dataUnit.name,
            })),
          );
        }
      });
      return results;
    };
    setFilterUseTag(handleDataUnitToTags(dataUnits));
    setCTagsMap(
      dataUnits?.reduce((acc, cur) => {
        cur.tagList?.forEach((tag) => {
          acc[getMetaTagUniqueId(tag)] = tag;
        });
        return acc;
      }, {} as Record<number, DataUnitTagVO>),
    );
  };

  useEffect(() => {
    if (app.appType === AppType.Approve && !dataId) {
      app.formVersionId && fetchFormVersion(app.formVersionId);
    }
  }, [app]);

  const checkDynimcparams = (dataUnit) => {
    let flag = false;
    const map = {};
    const { conditionGroups, dataUnitId } = dataUnit || {};
    (conditionGroups || []).map((group, groupIndex) => {
      if (group.conditionItems?.length > 0) {
        group.conditionItems.map((item, itemIndex) => {
          if (item.valueType && item.valueType === FILTER_VALUE_TYPE.VARIABLE_DYNAMIC) {
            map[`${dataUnitId}-${groupIndex}-${itemIndex}`] = {
              ...item,
              hasDynimcParams: true,
            };
            item['_enabled'] = true;
          }
          return item;
        });
      }
      return group;
    });
    if (Object.keys(map).length > 0) {
      flag = true;
    }
    return {
      flag,
      disabledMap: map,
    };
  };
  // 是否校验过动态入参，是否有动态入参
  const [hasChecked, hasConfigDynimcParams, dynamicConditionMap] = useMemo(() => {
    if (!formVersion) {
      return [false, false];
    }
    if (!isEmpty(formVersion)) {
      const hadDynicmParams = formVersion?.dataUnits?.some((dataUnit) => checkDynimcparams(dataUnit)?.flag);
      const map = formVersion?.dataUnits?.reduce((acc, dataUnit) => {
        acc = {
          ...acc,
          ...checkDynimcparams(dataUnit)?.disabledMap,
        };
        return acc;
      }, {});
      console.log('paramsMap-----', map);
      return [true, hadDynicmParams, map];
    }
    return [false, false, {}];
  }, [formVersion?.formId]);

  const filtersHasAllConditions = useMemo(() => {
    if (filters?.allConditionGroups) {
      return {
        ...filters,
        conditionGroups: filters?.allConditionGroups,
      };
    }
    return filters;
  }, [filters]);

  useEffect(() => {
    if (hasChecked && hasConfigDynimcParams) {
      setFilterOpen(true);
      // setFinishCfgParams(false)
    }
    if (hasChecked && !hasConfigDynimcParams) {
      // 如果没有动态入参无需自动打开
      setFinishCfgParams(true);
    }
  }, [hasChecked, hasConfigDynimcParams]);

  console.log('check-------', needCheck, hasChecked, finishCfgParams, (hasChecked && finishCfgParams) || !needCheck);
  if (!hasChecked && needCheck) {
    return null;
  }

  const render = () => {
    console.log('check-------', app);
    if (app.appType === AppType.SUBMIT) {
      return (
        <AppForm
          formId={app.formId}
          formVersionId={app.formVersionId}
          post={post}
          isPreview={isPreview}
          dataId={dataId}
          isReturn={isReturn}
          bizId={bizId}
          bizType={bizType}
          dynamicConditionMap={dynamicConditionMap}
          isSameGroup={isSameGroup}
          isSamePiece={isSamePiece}
          allDataIds={allDataIds}
          filters={tableFilters}
          dataGroupOption={dataGroupOption}
        />
      );
    } else if (app.appType === AppType.Approve) {
      return (
        <AppTable
          formId={app.formId}
          formVersionId={app.formVersionId}
          post={post}
          isPreview={isPreview}
          formVersion={formVersion}
          cTagsMap={cTagsMap}
          setFilterOpen={setFilterOpen}
          dynamicConditionMap={dynamicConditionMap}
          filters={filtersHasAllConditions}
        />
      );
    }
    return null;
  };
  if (hasChecked || !needCheck) {
    return (
      <>
        {
          // && ((!finishCfgParams && hasConfigDynimcParams) || finishCfgParams)
          // 只有配置了动态入参的才需要自动打开配置面板，同时不打开审批表单
          filterOpen && ((!finishCfgParams && hasConfigDynimcParams) || finishCfgParams) && app.appType === AppType.Approve && !dataId && (
            <DataQuerySetting
              isMainDataUnit={filters?.isMainDataUnit}
              dataUnitName={filters?.name}
              dataUnitId={filters?.dataUnitId}
              disabled
              additionalConditionGroups={filters?.additionalConditionGroups || []}
              tags={filterUseTag}
              canRelateTags={filterUseTag}
              conditionGroups={filters?.conditionGroups}
              extConfig={filters?.extConfig}
              onCancel={() => {
                setFinishCfgParams(true);
                setFilterOpen(false);
              }}
              onOk={(values) => {
                setFilterOpen(false);
                setFinishCfgParams(true);
                // const { allConditionGroups } = values
                setFilters((prev) => ({
                  ...prev,
                  ...values,
                }));
              }}
            />
          )
        }
        {((hasChecked && finishCfgParams) || !needCheck) && render()}
      </>
    );
  }
};

export default AppApply;
