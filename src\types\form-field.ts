import { AddressRange } from '@/components/meta-dynamic-item/address/const';
import { WidgetType } from '@/components/meta-dynamic-item/text/const';
import { CodeDateType, MetaDataType } from '@/const/metadata';
import {
  DateFormatType,
  OperatorType,
} from '@/pages/app-designer/components/right-panel/form-design/property-config/form-item-config/form-item-common-config/default-value-setting';
import { CalcFieldVO } from './calc-field';
import {
  MetaDataAddressDTO,
  MetaDataCodeDTO,
  MetaDataDateTimeDTO,
  MetaDataFileDTO,
  MetaDataNumberDTO,
  MetaDataObjectDTO,
  MetaDataTextDTO,
  MetaDataUnitDTO,
} from './metadata';

/**
 * com.ideal.gwy.datasource.model.request.form.FormFieldRequest
 *
 * FormFieldRequest
 */
export type FormField = {
  /**
   * 标签code 值，即标签在数据单元物理表对应的列名
   */
  code?: null | string;
  /**
   * 其他输入限制（字数限制、输入项来源等）
   */
  config?: FormTagConfig;
  /**
   * 标签字段所属的数据单元ID
   */
  dataUnitId?: number | null;
  /**
   * 字段配置
   */
  fieldConfig?: CalcFieldVO;
  /**
   * 标签别名
   */
  fieldName?: null | string;
  /**
   * 输入默认配置
   */
  inputDefaultConfig?: FormTagInputDefaultConfigRequest;
  /**
   * 排序（组内）
   */
  orderNum?: number | null;
  /**
   * 输入提示
   */
  placeholder?: null | string;
  /**
   * 是否只读（对应：输入、输出）
   */
  readonly?: boolean | null;
  /**
   * 是否必填
   */
  required?: boolean | null;
  /**
   * 来源ID(标签ID，组合字段ID)
   */
  sourceId?: number | null;
  /**
   * 来源类型（标签、字段）
   */
  sourceType?: SourceType;
  /**
   * 组件宽度
   */
  widgetWith?: null | string;
  [property: string]: any;
};

/**
 * 输入默认配置
 *
 * FormTagInputDefaultConfigRequest
 */
export type FormTagInputDefaultConfigRequest = {
  /**
   * 表单数据单元查询
   */
  dataUnit?: any;
  /**
   * 日期类型查询对象
   */
  dateConfig?: FormTagInputDefaultDateConfigRequest;
  /**
   * 开关
   */
  open?: boolean | null;
  /**
   * 默认类型
   */
  type?: InputDefaultConfigType;
  /**
   * 默认值
   */
  value?: { [key: string]: any };
  [property: string]: any;
};
/**
 * 日期类型查询对象
 *
 * FormTagInputDefaultDateConfigRequest
 */
export type FormTagInputDefaultDateConfigRequest = {
  /**
   * 系统时间默认值
   */
  systemTimeDefault?: SystemTimeDefault;
  /**
   * 查询类型
   */
  type?: DateConfigType;
  [property: string]: any;
};

/**
 * 查询类型
 */
export enum DateConfigType {
  Custom = 'custom',
  System = 'system',
}

/**
 * 系统时间默认值
 *
 * SystemTimeDefault
 */
export type SystemTimeDefault = {
  /**
   * 日期格式 1-年，2-月，3-日
   */
  dateFormat?: DateFormatType | null;
  /**
   * 运算值(偏移量)
   */
  delta?: number | null;
  /**
   * 运算符(0-当日,-1-往前)
   */
  operator?: OperatorType | null;
  [property: string]: any;
};

/**
 * 默认类型
 */
export enum InputDefaultConfigType {
  DefaultValue = 'default_value',
  Query = 'query',
}

/**
 * 来源类型（标签、字段）
 */
export enum SourceType {
  Field = 'field',
  Tag = 'tag',
  // combine_field = 'combine_field',
}

export type FormTagConfig = {
  /**
   * 类型
   */
  type?: MetaDataType;

  metaDataTextDTO?: MetaDataTextDTO; // 文本配置
  widgetType?: WidgetType; // 展示类型
  multipleChoice?: boolean; // 是否多选
  appoint?: boolean; // 是否指定选项

  metaDataNumberDTO?: MetaDataNumberDTO; // 数字配置
  decimalPlaces?: number; // 小数位数

  metaDataDateTimeDTO?: MetaDataDateTimeDTO; // 时间配置
  dateForm?: CodeDateType; // 日期格式

  metaDataObjectDTO?: MetaDataObjectDTO; // 对象配置
  // multipleChoice?: boolean;
  conditionGroups?: any[]; // 筛选条件

  metaDataUnitDTO?: MetaDataUnitDTO;
  metaDataCodeDTO?: MetaDataCodeDTO;
  metaDataFileDTO?: MetaDataFileDTO;
  // 地址层级
  addressRange?: AddressRange;
  metaDataAddressDTO?: MetaDataAddressDTO;

  [property: string]: any;
};
