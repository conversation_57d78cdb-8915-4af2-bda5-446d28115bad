import { AddressRange } from '@/components/meta-dynamic-item/address/const';
import { WidgetType } from '@/components/meta-dynamic-item/text/const';
import { MetaDataType, MetaTagType, MetaUnitType, TextInputType } from '@/const/metadata';
import { SelectedStateType } from '@/pages/app-designer/const';
import { IAppDesignerContext } from '@/pages/app-designer/context';
import { FormField, SourceType } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { getMetaTagUniqueId } from '@/utils/metadata';
import { message } from '@gwy/components-web';
import { produce } from 'immer';
import { cloneDeep } from 'lodash-es';
import { DropResult, DropResultName } from './tags-config/draggable';

export const createFormField = (tag: DataUnitTagVO) => {
  const { tagMetaDataConfig } = tag;
  const { type } = tagMetaDataConfig || {};

  const field: FormField = {};
  field.sourceType = SourceType.Tag;
  field.sourceId = tag.tagId;
  field.code = tag.code;
  field.dataUnitId = tag.dataUnitId;
  field.readonly = tag.$$dataUnit?.type === MetaUnitType.General || tag.type === MetaTagType.System;
  field.required = true;
  field.widgetWith = '50%';
  field.config = cloneDeep(tagMetaDataConfig || {}) as any;

  // 默认数据设置
  if (type === MetaDataType.Text) {
    const { inputType } = tagMetaDataConfig?.metaDataTextDTO || {};
    if (inputType === TextInputType.enum) {
      field.config.multipleChoice = true;
      field.config.appoint = false;
      field.config.metaDataTextDTO.enumList = [];
    } else if (inputType === TextInputType.manual) {
      field.config.widgetType = WidgetType.SHORT;
    }
  } else if (type === MetaDataType.Number) {
    field.config.decimalPlaces = 0;
    field.config.metaDataNumberDTO.max = field.config.metaDataNumberDTO.max
      ? Number(field.config.metaDataNumberDTO.max)
      : field.config.metaDataNumberDTO.max;
    field.config.metaDataNumberDTO.min = field.config.metaDataNumberDTO.min
      ? Number(field.config.metaDataNumberDTO.min)
      : field.config.metaDataNumberDTO.min;
  } else if (type === MetaDataType.DateTime) {
  } else if (type === MetaDataType.Object) {
    field.config.multipleChoice = false;
  } else if (type === MetaDataType.Unit) {
    field.config.metaDataUnitDTO.max = field.config.metaDataUnitDTO.max ? Number(field.config.metaDataUnitDTO.max) : field.config.metaDataUnitDTO.max;
    field.config.metaDataUnitDTO.min = field.config.metaDataUnitDTO.min ? Number(field.config.metaDataUnitDTO.min) : field.config.metaDataUnitDTO.min;
  } else if (type === MetaDataType.Code) {
  } else if (type === MetaDataType.Address) {
    field.config.addressRange = AddressRange.region;
  } else if (type === MetaDataType.File) {
    field.config.metaDataFileDTO.fileNum = Number(field.config.metaDataFileDTO.fileNum);
    field.config.metaDataFileDTO.sizeMax = Number(field.config.metaDataFileDTO.sizeMax);
  }

  return field;
};

export const addFormItem = (
  { formField, sourceType }: { formField: FormField; sourceType: SourceType },
  dropResult: DropResult,
  appContext: IAppDesignerContext,
) => {
  const {
    tagsMap,
    configuredFields,
    setConfiguredFields,
    combineTagConfig,
    setCombineTagConfig,
    tableList,
    setTableList,
    selectedState,
    setSelectedState,
  } = appContext;

  if (dropResult.name === DropResultName.BaseGroup) {
    // 基础分组
    // 已经存在不能拖入
    if (
      configuredFields.some((f) => getMetaTagUniqueId(f) === getMetaTagUniqueId(formField)) ||
      combineTagConfig?.fields?.some((f) => getMetaTagUniqueId(f) === getMetaTagUniqueId(formField))
    ) {
      return;
    }
    setConfiguredFields((pre) => [...pre, formField]);
    setSelectedState({
      type: SelectedStateType.baseGroupItem,
      formItemId: getMetaTagUniqueId(formField),
      formItemType: sourceType,
    });
  } else if (dropResult.name === DropResultName.CombineTag) {
    // 组合标签
    // 基础分组中配置的标签所在的数据单元的标签，才可拖入
    if (!configuredFields?.map((f) => f.dataUnitId)?.includes(formField.dataUnitId)) {
      message.warning('请先在基础分组中拖入相同数据单元的其他标签');
      return;
    }
    if (sourceType === SourceType.Tag) {
      // 系统标签不能拖入
      if (tagsMap[getMetaTagUniqueId(formField)]?.type === MetaTagType.System) {
        return;
      }
    }
    // 已经存在不能拖入
    if (
      configuredFields.some((f) => getMetaTagUniqueId(f) === getMetaTagUniqueId(formField)) ||
      combineTagConfig?.fields?.some((f) => getMetaTagUniqueId(f) === getMetaTagUniqueId(formField))
    ) {
      return;
    }
    setSelectedState({
      type: SelectedStateType.combineTagItem,
      formItemId: getMetaTagUniqueId(formField),
      formItemType: sourceType,
    });
    const newCombineTagConfig = produce(combineTagConfig, (draft) => {
      let fields = draft.fields || [];
      fields.push(formField);
      draft.fields = fields;
    });
    setCombineTagConfig(newCombineTagConfig);
  } else if (dropResult.name === DropResultName.DataTable) {
    // 数据表格
    const table = tableList?.find((t) => t.id === dropResult.id);
    if (table.fields?.some((f) => getMetaTagUniqueId(f) === getMetaTagUniqueId(formField))) {
      return;
    }
    setSelectedState({
      type: SelectedStateType.tableItem,
      formItemId: getMetaTagUniqueId(formField),
      formItemType: sourceType,
      tableId: dropResult.id,
    });
    const newTableList = produce(tableList, (draft) => {
      const table = draft.find((t) => t.id === dropResult.id);
      let fields = table.fields || [];
      fields.push(formField);
      table.fields = fields;
    });
    setTableList(newTableList);
  }
};
