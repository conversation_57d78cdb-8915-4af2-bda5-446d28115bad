import { systemDatametaAPI } from '@/services';
import { isEmpty } from 'lodash-es';
import { useEffect, useMemo, useState } from 'react';
import DataList from '../app-apply/components/app-table/data-list';
interface Iprops {
  records?: any;
  combineConfig?: any;
  tableConfig?: any;
  cTagsMap: any;
  formDataDraftId: any;
  dataUnits?: any;
}
const TableAndCombine = (props: Iprops) => {
  const { records, combineConfig, tableConfig, cTagsMap, formDataDraftId, dataUnits } = props;
  console.log(dataUnits, 'dataUnits--------');
  const [datasource, setDataSource] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
  });
  const [configFields] = useMemo(() => {
    let cfgFields = [];
    if (!isEmpty(tableConfig)) {
      cfgFields = tableConfig?.fields;
    }
    if (!isEmpty(combineConfig)) {
      cfgFields = combineConfig?.fields;
    }
    return [cfgFields];
  }, [combineConfig, tableConfig]);

  const findRecords = async (formDataDraftId, tableId) => {
    const data = await systemDatametaAPI.getOrgDataUnitApproveData(formDataDraftId, tableId);
    // setFormDetail((prev) => ({ ...prev, ...data }));
    const { draftItems } = data || {};
    setDataSource(draftItems?.map((item) => item?.record));
  };

  useEffect(() => {
    if (!isEmpty(combineConfig)) {
      setDataSource(records?.map((item) => item?.record)?.filter((record) => record.data_type === 2));
    }
    if (!isEmpty(tableConfig) && formDataDraftId) {
      findRecords(formDataDraftId, tableConfig?.id);
    }
  }, [records, combineConfig, tableConfig, formDataDraftId]);

  const configDataUnits = useMemo(() => {
    const tableDataUnits = tableConfig?.dataUnits;
    return dataUnits ? dataUnits : tableDataUnits;
  }, [dataUnits, tableConfig]);
  return (
    <div>
      <DataList
        isPreview
        formFields={configFields}
        cTagsMap={cTagsMap}
        dataSource={datasource}
        // dataUnits={configDataUnits}
        pagination={{
          total: datasource?.length,
          current: pagination.current,
          pageSize: pagination.pageSize,
          onChange: (page, pageSize) => {
            setPagination((pre) => ({ ...pre, current: page, pageSize }));
          },
        }}
        toolBarRender={false}
      />
    </div>
  );
};

export default TableAndCombine;
