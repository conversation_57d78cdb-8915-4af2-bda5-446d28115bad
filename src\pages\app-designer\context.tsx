import { IEditorRef } from '@/components/onlyoffice-editor';
import { FormButtonConfig } from '@/services/form-button';
import { FormVO } from '@/types/app';
import { CalcFieldVO } from '@/types/calc-field';
import { FormCombineTagConfig } from '@/types/combine-tag';
import { DataUnitBaseVO } from '@/types/data-unit';
import { FormField } from '@/types/form-field';
import { FormTableConfig } from '@/types/form-table';
import { OrgDataUnitTagListVO, OrgDataUnitTagVO } from '@/types/org-data-unit';
import { Post } from '@/types/post';
import React from 'react';
import { LeftPanelTabType } from './components/left-panel';
import { RightPanelTabType } from './components/right-panel';
import { PropertyConfigRef } from './components/right-panel/form-design/property-config';
import { AppType, FormType, SelectedState } from './const';

export type IAppDesignerContext = {
  setLoading?: React.Dispatch<React.SetStateAction<boolean>>;
  post?: Post; // 当前岗位
  appInfo?: FormVO; // 应用信息
  formVersionId?: any; // 表单版本id
  name?: string; // 表单名称
  setName?: React.Dispatch<React.SetStateAction<string>>;
  formType?: FormType; // 表单类型
  setFormType?: React.Dispatch<React.SetStateAction<FormType>>;
  appType?: AppType; // 应用类型
  setAppType?: React.Dispatch<React.SetStateAction<AppType>>;

  wordReadonly?: boolean; // word是否只读
  setWordReadonly?: React.Dispatch<React.SetStateAction<boolean>>;
  notifyConfig?: any; // 通知配置
  setNotifyConfig?: React.Dispatch<React.SetStateAction<any>>;
  editorRef?: React.RefObject<IEditorRef>; // 编辑器ref
  handleUpdate?: (isAuto?: boolean) => Promise<number>;
  propertyConfigRef?: React.RefObject<PropertyConfigRef>; // 表单配置ref
  getPropertyConfigRef?: () => PropertyConfigRef;
  fetchAllConfiguredDataUnitsWithTags?: () => void; // 获取已配置的数据单元的标签

  availableDataUnits?: DataUnitBaseVO[]; // 可用的数据单元
  setAvailableDataUnits?: React.Dispatch<React.SetStateAction<DataUnitBaseVO[]>>;
  configuredDataUnits?: DataUnitBaseVO[]; // 已配置的数据单元
  setConfiguredDataUnits?: React.Dispatch<React.SetStateAction<DataUnitBaseVO[]>>;
  allConfiguredDataUnitsWithTags?: OrgDataUnitTagListVO[]; // 已配置的数据单元-带标签
  setAllConfiguredDataUnitsWithTags?: React.Dispatch<React.SetStateAction<OrgDataUnitTagListVO[]>>;
  tagsMap?: Record<string, OrgDataUnitTagVO>; // 标签映射
  baseGroupName?: string; // 基础分组名称
  setBaseGroupName?: React.Dispatch<React.SetStateAction<string>>;
  configuredFields?: FormField[]; // 基础分组已配置的表单项
  setConfiguredFields?: React.Dispatch<React.SetStateAction<FormField[]>>;
  allDataUnitsWithTags?: OrgDataUnitTagListVO[]; // 全部的有权限的数据单元及标签列表
  combineTagConfig?: FormCombineTagConfig; // 组合标签配置
  setCombineTagConfig?: React.Dispatch<React.SetStateAction<FormCombineTagConfig>>;
  tableList?: FormTableConfig[]; // 数据表格列表
  setTableList?: React.Dispatch<React.SetStateAction<FormTableConfig[]>>;
  buttonConfigs?: FormButtonConfig[]; // 按钮列表
  setButtonConfigs?: React.Dispatch<React.SetStateAction<FormButtonConfig[]>>;
  calcFields?: CalcFieldVO[]; // 字段列表
  setCalcFields?: React.Dispatch<React.SetStateAction<CalcFieldVO[]>>;
  configuredFormFields?: FormField[]; // 整张表单已配置的表单字段

  leftPanelTabType?: LeftPanelTabType; // 左面板tab
  setLeftPanelTabType?: React.Dispatch<React.SetStateAction<LeftPanelTabType>>;
  rightPanelTabType?: RightPanelTabType; // 右面板tab
  setRightPanelTabType?: React.Dispatch<React.SetStateAction<RightPanelTabType>>;
  selectedState?: SelectedState; // 选中的项
  setSelectedState?: React.Dispatch<React.SetStateAction<SelectedState>>;

  groupFlag?: boolean; // 是否聚合
  setGroupFlag?: React.Dispatch<React.SetStateAction<boolean>>;
  aggregationMainDataUnits?: DataUnitBaseVO[]; // 聚合-可选的主表单
  setAggregationMainDataUnits?: React.Dispatch<React.SetStateAction<DataUnitBaseVO[]>>;
  aggregationMainDataUnitId?: number; // 聚合-主表单
  setAggregationMainDataUnitId?: React.Dispatch<React.SetStateAction<number>>;
  aggregationSubDataUnits?: DataUnitBaseVO[]; // 聚合-子表单
  setAggregationSubDataUnits?: React.Dispatch<React.SetStateAction<DataUnitBaseVO[]>>;
  aggregationSubDataUnitIds?: number[]; // 聚合-子表单
  setAggregationSubDataUnitIds?: React.Dispatch<React.SetStateAction<number[]>>;
  currSubDataUnitId?: number; // 当前选中的子表单
  setCurrSubDataUnitId?: React.Dispatch<React.SetStateAction<number>>;
  handleAggregationSubUpdate?: (dataUnitId: number) => Promise<void>; // 修改子表单
  fetchAggregationSubDataUnitConfig?: (dataUnitId: number) => void; // 获取子表单配置
};

export const AppDesignerContext = React.createContext<IAppDesignerContext>({});
