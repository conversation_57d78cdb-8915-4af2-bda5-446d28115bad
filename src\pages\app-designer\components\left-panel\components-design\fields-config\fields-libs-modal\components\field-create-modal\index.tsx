import { createField } from '@/services/calc-field';
import { CalcType, FieldType } from '@/types/calc-field';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { Form, Input, Modal, Radio, Tooltip } from '@gwy/components-web';
import { memo, useEffect } from 'react';
import CalcCalc from '../calc-calc';
import FmlCalc from '../fml-calc';
import { FieldCreateContext } from './context';
import styles from './index.less';

interface IProps {
  postId?: string;
  configuredDataUnits?: any[];
  allDataUnitsWithTags?: any[];
  fieldList?: any[];
  record?: any;
  onCancel?: () => void;
  onOk?: (values?: any) => void;
}

const FieldCreateModal = memo<IProps>(({ postId, configuredDataUnits, allDataUnitsWithTags, fieldList, record, onCancel, onOk }) => {
  const [form] = Form.useForm();
  const { validateFields, setFieldsValue } = form;
  const calcType = Form.useWatch('calcType', form);
  const fieldType = Form.useWatch('fieldType', form);

  useEffect(() => {
    if (record) {
      setFieldsValue({
        ...record,
        createPostId: postId,
      });
    }
  }, [record, setFieldsValue]);

  const handleOk = async () => {
    try {
      const values = await validateFields();

      if (record) {
        onOk(values);
        return;
      }
      await createField({
        ...values,
      });
      onOk();
    } catch (error) {
      const { errorFields = [] } = error;
      if (errorFields.length) {
        form.scrollToField(errorFields[0].name);
      }
    }
  };

  return (
    <Modal
      open
      title="计算字段"
      okText="确定"
      okButtonProps={{
        type: 'primary',
      }}
      onOk={handleOk}
      onCancel={onCancel}
      width={760}
      styles={{
        body: {
          height: 680 - 80,
        },
      }}
    >
      <div className={styles.container}>
        <div className={styles.content}>
          <Form form={form}>
            <Form.Item name="createPostId" initialValue={postId} hidden></Form.Item>
            <Form.Item name="fieldId" initialValue={record?.fieldId} hidden></Form.Item>
            <Form.Item label="字段名称" name="fieldName" rules={[{ required: true, message: '请输入' }]}>
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item label="字段类型" name="fieldType" initialValue={FieldType.FormField} rules={[{ required: true, message: '请选择' }]}>
              <Radio.Group style={{ display: 'flex', columnGap: 30 }}>
                <Radio value={FieldType.FormField}>表单字段</Radio>
                <Radio value={FieldType.IndependentField}>独立字段</Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item
              label="计算方式"
              name="calcType"
              initialValue={CalcType.FormulasCalc}
              rules={[{ required: true, message: '请选择' }]}
              style={{ marginBottom: 20 }}
            >
              <Radio.Group style={{ display: 'flex', columnGap: 30 }}>
                <Radio value={CalcType.FormulasCalc}>
                  公式计算&nbsp;
                  <Tooltip title="可选择标签/字段配置公式，依据各个标签查询到的数据使用函数，动态计算得出数值。">
                    <QuestionCircleOutlined />
                  </Tooltip>
                </Radio>
                <Radio value={CalcType.Calculation}>
                  阶梯计算&nbsp;
                  <Tooltip title="可先计算一个字段是否符合相应的条件，再根据条件分别流转至下一个计算公式，再得出计算结果值的多阶段计算。">
                    <QuestionCircleOutlined />
                  </Tooltip>
                </Radio>
              </Radio.Group>
            </Form.Item>
            {/* 公式计算 */}
            {calcType === CalcType.FormulasCalc && (
              <FieldCreateContext.Provider
                value={{
                  configuredDataUnits,
                  allDataUnitsWithTags,
                  fieldList,
                  fieldType,
                }}
              >
                <Form.Item name="formulasCalcs">
                  <FmlCalc />
                </Form.Item>
              </FieldCreateContext.Provider>
            )}
            {/* 阶梯计算 */}
            {calcType === CalcType.Calculation && (
              <FieldCreateContext.Provider
                value={{
                  configuredDataUnits,
                  allDataUnitsWithTags,
                  fieldList,
                  fieldType,
                }}
              >
                <CalcCalc />
              </FieldCreateContext.Provider>
            )}
          </Form>
        </div>
      </div>
    </Modal>
  );
});

export default FieldCreateModal;
