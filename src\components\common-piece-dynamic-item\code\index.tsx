import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { Select } from 'antd';
import { memo } from 'react';
import { UseIn } from '../const';

export type CodeMetaProps = {
  useIn?: UseIn;
  tag?: DataUnitTagVO;
  field?: FormField;
  value?: any;
  onChange?: (value) => void;
  isPreview?: boolean; // 仅预览模式
  options?: any;
};

const CodeMeta = memo(({ useIn, tag, field, value, onChange, isPreview, options }: CodeMetaProps) => {
  const { value: raw_value } = value || {};
  console.log(options, 'options------------');
  return (
    <Select
      options={[]}
      placeholder="请选择"
      value={raw_value}
      onChange={(e) => {
        const value = options?.find((item) => item === e);
        onChange?.(value);
      }}
    />
  );
});

export default CodeMeta;
