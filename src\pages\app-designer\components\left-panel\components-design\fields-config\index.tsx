import { AppDesignerContext } from '@/pages/app-designer/context';
import { selectField } from '@/services/calc-field';
import { CalcFieldVO } from '@/types/calc-field';
import { FormField, SourceType } from '@/types/form-field';
import { SettingOutlined } from '@ant-design/icons';
import { ConfigProvider, Empty, message } from '@gwy/components-web';
import { produce } from 'immer';
import { useContext, useState } from 'react';
import SectionHeader from '../../../section-header';
import Draggable, { DropResult } from '../tags-config/draggable';
import { addFormItem } from '../utils';
import FieldItem from './field-item';
import FieldsLibsModal from './fields-libs-modal';
import styles from './index.less';

const FieldsConfig = () => {
  const appDesignerContext = useContext(AppDesignerContext);
  const {
    post,
    configuredDataUnits,
    allDataUnitsWithTags,
    calcFields,
    setCalcFields,
    configuredFields,
    setConfiguredFields,
    combineTagConfig,
    setCombineTagConfig,
    tableList,
    setTableList,
  } = appDesignerContext;

  const [fieldConfigModal, setFieldConfigModal] = useState<{
    open?: boolean;
  }>({
    open: false,
  });

  const createFormField = (item: CalcFieldVO) => {
    const field: FormField = {};
    field.sourceType = SourceType.Field;

    field.fieldConfig = {
      ...item,
      formFieldConfig: {
        decimalNum: 0,
        widgetWith: '50%',
      },
    };
    // item.formFieldConfig = {
    //   decimalNum: 0,
    //   widgetWith: '50%',
    // };
    // field.fieldConfig = item;

    return field;
  };

  const addCalcField = (calcField: CalcFieldVO, dropResult: DropResult) => {
    // 计算字段，整个应用只能添加一次
    if (
      configuredFields?.some((f) => f.fieldConfig?.fieldId === calcField.fieldId) ||
      combineTagConfig?.fields?.some((f) => f.fieldConfig?.fieldId === calcField.fieldId) ||
      tableList?.some((table) => table.fields?.some((f) => f.fieldConfig?.fieldId === calcField.fieldId))
    ) {
      message.warning('计算字段只能添加一次');
      return;
    }
    addFormItem({ formField: createFormField(calcField), sourceType: SourceType.Field }, dropResult, appDesignerContext);
  };

  return (
    <div>
      <SectionHeader
        title="字段库"
        rightExtra={
          <span
            style={{ color: '#4D7BF6', fontWeight: 'normal', fontSize: 12, display: 'flex', alignItems: 'center' }}
            onClick={() => setFieldConfigModal({ open: true })}
          >
            <SettingOutlined style={{ marginRight: 4 }} />
            管理
          </span>
        }
      />
      <div className={styles.listWrapper}>
        <div className={styles.list}>
          {calcFields?.map((field) => {
            // const active = cFields?.some((f) => f.sourceId === field.id);
            return (
              <div key={field.id} className={styles.item}>
                <Draggable<CalcFieldVO> item={field} onDragEnd={(item, dropResult) => addCalcField(field, dropResult)}>
                  <FieldItem
                    field={field}
                    canDelete
                    onDelete={(e) => {
                      e.stopPropagation();
                      setCalcFields(calcFields.filter((f) => f.fieldId !== field.fieldId));
                      setConfiguredFields(configuredFields.filter((f) => f.fieldConfig?.fieldId !== field.fieldId));
                      if (combineTagConfig) {
                        setCombineTagConfig(
                          produce(combineTagConfig, (draft) => {
                            draft.fields = draft.fields.filter((f) => f.fieldConfig?.fieldId !== field.fieldId);
                          }),
                        );
                      }
                      setTableList(
                        produce(tableList, (draft) => {
                          draft.forEach((table) => {
                            table.fields = table.fields.filter((f) => f.fieldConfig?.fieldId !== field.fieldId);
                          });
                        }),
                      );
                    }}
                  />
                </Draggable>
              </div>
            );
          })}

          {calcFields?.length === 0 && <Empty description="请先去【管理】新增字段" style={{ margin: '24px auto' }} />}
        </div>
      </div>

      {fieldConfigModal.open && (
        <ConfigProvider theme={{ token: { fontSize: 14 } }}>
          <FieldsLibsModal
            post={post}
            configuredDataUnits={configuredDataUnits}
            allDataUnitsWithTags={allDataUnitsWithTags}
            onCancel={() => {
              setFieldConfigModal({ open: false });
            }}
            onOk={async (fieldIds) => {
              try {
                const data = await selectField({ postId: post.postId, fieldIds });
                setCalcFields((pre) => [...pre, ...data]);
                setFieldConfigModal({ open: false });
              } catch (err) {}
            }}
          />
        </ConfigProvider>
      )}

      {/* {fieldCreateModal.open && (
        <FieldCreateModal
          postId={post.postId}
          configuredDataUnits={configuredDataUnits}
          allDataUnitsWithTags={allDataUnitsWithTags}
          record={fieldCreateModal.record}
          onCancel={() => setFieldCreateModal({ open: false })}
          onOk={(values) => {
            setFieldCreateModal({ open: false });
            setCalcFields((pre) => {
              const targetField = pre.find((field) => field.fieldId === values.fieldId);

              Object.assign(targetField, values);
              return [...pre];
            });
          }}
        />
      )} */}
    </div>
  );
};

export default FieldsConfig;
