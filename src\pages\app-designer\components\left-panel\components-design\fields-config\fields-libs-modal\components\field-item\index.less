.container {
  position: relative;
  display: flex;
  align-items: center;

  .detail {
    position: relative;
    display: flex;
    align-items: center;
    flex: 1;
    height: 36px;
    max-width: calc(100% - 32px);
    padding: 0 10px;
    border: 1px solid #e5e6eb;
    border-radius: 2px;
    cursor: pointer;

    .fieldName {
      flex: 1;
      display: inline-block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-right: 20px;
    }

    .icon {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 28px;
      height: 16px;
      font-size: 10px;
      color: #00b42a;
      background: #e8ffea;
      border: 1px solid #00b42a;
      border-radius: 2px;
    }
  }

  .closeIcon {
    display: none;
    position: absolute;
    top: -8px;
    right: -8px;
    color: #ccc;
    font-size: 16px;
  }

  &:hover {
    .closeIcon {
      display: block;
    }
  }
}
