import { QuestionCircleOutlined } from '@ant-design/icons';
import { Button, Form, Input, Radio, Select, Switch, TimePicker, Tooltip } from '@gwy/components-web';
import dayjs from 'dayjs';
import { useEffect } from 'react';
import styles from './index.less';

export const notifyConfigKey = 'notifyConfig';

const enum NotifyType {
  newPending = 'newPending',
  interval = 'interval',
  timer = 'timer',
}

const enum NotifyInterval {
  minute_5 = 'minute_5',
  minute_15 = 'minute_15',
  hour_1 = 'hour_1',
  hour_3 = 'hour_3',
  hour_6 = 'hour_6',
}

const enum TimerType {
  fixed = 'fixed',
  dynamic = 'dynamic',
}

export type messageConfigProps = {
  value?: any;
  onChange?: (values) => void;
};

const MessageConfig = ({ value, onChange }: messageConfigProps) => {
  const [form] = Form.useForm();

  const notifyEnabled = Form.useWatch(['notifyEnabled'], form);
  const notifyType = Form.useWatch(['notifyType'], form);
  const timerType = Form.useWatch('timerType', form);
  const cycleType = Form.useWatch('cycleType', form);
  const cycleTypeVal1 = Form.useWatch('cycleTypeVal1', form);

  // 根据传入的月份，返回对应的天数的options
  const getDaysByMonth = (month = 1) => {
    let _month = month;
    // 如何month不在1-12之间，month为1
    if (_month < 1 || _month > 12) {
      _month = 1;
    }
    const days = new Date(2021, _month, 0).getDate();

    return Array.from({ length: days }, (_, i) => ({ label: `${i + 1}号`, value: i + 1 }));
  };

  // 根据传参生成不同的options
  const genOptions = (type) => {
    switch (type) {
      case 'week':
        return [
          { label: '周一', value: 1 },
          { label: '周二', value: 2 },
          { label: '周三', value: 3 },
          { label: '周四', value: 4 },
          { label: '周五', value: 5 },
          { label: '周六', value: 6 },
          { label: '周日', value: 7 },
        ];
      case 'month':
        return Array.from({ length: 31 }, (_, i) => ({ label: `${i + 1}号`, value: i + 1 }));
      case 'year':
        return Array.from({ length: 12 }, (_, i) => ({ label: `${i + 1}月`, value: i + 1 }));
      default:
        return [];
    }
  };

  const cycleTypeOpts = [
    { label: '天', value: 1 },
    {
      label: '周',
      value: 2,
      opts: genOptions('week'),
    },
    {
      label: '月',
      value: 3,
      opts: genOptions('month'),
    },
    {
      label: '年',
      value: 4,
      opts: genOptions('year'),
    },
  ];

  // 根据传入的type，返回对应的options
  const getOptionsByType = (type) => {
    return cycleTypeOpts[type - 1]?.opts || [];
  };

  const renderCycleTypeVal = () => {
    return (
      <>
        {cycleType > 1 && (
          <Form.Item style={{ marginBottom: 0, width: 80 }} name="cycleTypeVal1">
            <Select allowClear={false}>
              {getOptionsByType(cycleTypeOpts[cycleType - 1].value).map((c) => (
                <Select.Option value={c.value} key={c.value}>
                  {c.label}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        )}
        {cycleType === 4 && (
          <Form.Item style={{ marginBottom: 0, width: 80 }} name="cycleTypeVal2">
            <Select allowClear={false}>
              {getDaysByMonth(cycleTypeOpts[cycleType - 1]?.opts[cycleTypeVal1 - 1]?.value).map((c) => (
                <Select.Option value={c.value} key={c.value}>
                  {c.label}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        )}
      </>
    );
  };

  const getCycleTime = (values) => {
    const { cycleType, cycleTypeVal1, cycleTypeVal2, cycleTime } = values;

    if (cycleTime) {
      if (cycleType === 1) {
        return dayjs(values.cycleTime).format('YYYY-MM-DD HH:mm:ss');
      } else if (cycleType === 2) {
        return dayjs(values.cycleTime).day(cycleTypeVal1).format('YYYY-MM-DD HH:mm:ss');
      } else if (cycleType === 3) {
        return dayjs(values.cycleTime).date(cycleTypeVal1).format('YYYY-MM-DD HH:mm:ss');
      } else if (cycleType === 4) {
        return dayjs(values.cycleTime)
          .month(cycleTypeVal1 - 1)
          .date(cycleTypeVal2)
          .format('YYYY-MM-DD HH:mm:ss');
      }
    }
    return null;
  };

  const getCycleTypeVal = (cycleType, cycleTime) => {
    let cycleTypeVal1 = 1;
    let cycleTypeVal2 = 1;

    if (cycleType === 2) {
      cycleTypeVal1 = dayjs(cycleTime).day() === 0 ? 7 : dayjs(cycleTime).day();
    } else if (cycleType === 3) {
      cycleTypeVal1 = dayjs(cycleTime).date();
    } else if (cycleType === 4) {
      cycleTypeVal1 = dayjs(cycleTime).month() + 1;
      cycleTypeVal2 = dayjs(cycleTime).date();
    }
    return {
      cycleTypeVal1,
      cycleTypeVal2,
    };
  };

  useEffect(() => {
    const { cycleType, cycleTime } = value || {};
    const initCycleTime = cycleTime ? dayjs(dayjs(cycleTime).format('HH:mm'), 'HH:mm') : dayjs('00:00', 'HH:mm');
    form.setFieldsValue({
      ...value,
      cycleTime: initCycleTime,
      ...getCycleTypeVal(cycleType, cycleTime),
    });
  }, [value]);

  return (
    <div className={styles.container}>
      <Form
        form={form}
        onValuesChange={(changedValues, values) => {
          setTimeout(() => {
            const _values = form.getFieldsValue();
            onChange?.({
              ..._values,
              cycleTime: getCycleTime(_values),
            });
          }, 0);
        }}
      >
        <Form.Item colon={false} wrapperCol={{ style: { textAlign: 'right' } }} label="消息提醒" name={['notifyEnabled']} style={{ marginBottom: 6 }}>
          <Switch />
        </Form.Item>

        {notifyEnabled && (
          <>
            <Form.Item name={['notifyType']} initialValue={NotifyType.newPending}>
              <Radio.Group>
                <Radio value={NotifyType.newPending}>
                  新待办通知
                  <Tooltip overlay="待办消息数据实时抓取，推荐仅在表单存在少量审核时启用" mouseEnterDelay={0.3}>
                    <QuestionCircleOutlined style={{ marginLeft: 3 }} />
                  </Tooltip>
                </Radio>
                <Radio value={NotifyType.interval}>
                  间隔通知
                  <Tooltip overlay="配置待办消息数据定时抓取时间，推荐仅在表单存在大量审核时启用" mouseEnterDelay={0.3}>
                    <QuestionCircleOutlined style={{ marginLeft: 3 }} />
                  </Tooltip>
                </Radio>
                <Radio value={NotifyType.timer}>
                  定时通知
                  <Tooltip overlay="根据设置的定时触发时间，定时推送需待办的数据" mouseEnterDelay={0.3}>
                    <QuestionCircleOutlined style={{ marginLeft: 3 }} />
                  </Tooltip>
                </Radio>
              </Radio.Group>
            </Form.Item>

            {notifyType === NotifyType.interval && (
              <Form.Item layout="vertical" label="定时待办" name={['notifyInterval']} initialValue={NotifyInterval.minute_5}>
                <Radio.Group optionType="button">
                  <Radio value={NotifyInterval.minute_5}>5分钟</Radio>
                  <Radio value={NotifyInterval.minute_15}>15分钟</Radio>
                  <Radio value={NotifyInterval.hour_1}>1小时</Radio>
                  <Radio value={NotifyInterval.hour_3}>3小时</Radio>
                  <Radio value={NotifyInterval.hour_6}>6小时</Radio>
                </Radio.Group>
              </Form.Item>
            )}

            {notifyType === NotifyType.timer && (
              <>
                <Form.Item label="触发规则">
                  <Form.Item colon={false} style={{ marginBottom: 0, width: 140 }} name={'timerType'}>
                    <Select
                      allowClear={false}
                      onChange={(value) => {
                        if (value === 0) {
                          form.setFieldsValue({ cycle: 1 });
                        } else {
                          form.setFieldsValue({ cycle: undefined });
                        }
                      }}
                    >
                      <Select.Option value={TimerType.fixed}>固定值</Select.Option>
                      <Select.Option value={TimerType.dynamic}>动态值</Select.Option>
                    </Select>
                  </Form.Item>
                </Form.Item>
                <div>
                  {timerType === TimerType.dynamic ? (
                    <>
                      <Input.Group compact style={{ width: 'unset', marginTop: 8 }}>
                        <Button style={{ width: 30, padding: 0 }} type="text">
                          每
                        </Button>
                        <Form.Item style={{ marginBottom: 0, width: 40 }} name={'cycle'} required>
                          <Input />
                        </Form.Item>
                        <Form.Item style={{ marginBottom: 0, width: 60 }} name={'cycleType'}>
                          <Select allowClear={false} placeholder="请选择" options={cycleTypeOpts} />
                        </Form.Item>
                        {renderCycleTypeVal()}
                        <Form.Item style={{ marginBottom: 0, width: 80 }} name={'cycleTime'}>
                          <TimePicker allowClear={false} format={'HH:mm'} />
                        </Form.Item>
                      </Input.Group>
                    </>
                  ) : (
                    <>
                      <Form.Item style={{ marginBottom: 0, width: 80 }} name={'cycle'} hidden initialValue={1}>
                        <Input />
                      </Form.Item>
                      <Input.Group compact style={{ width: 'unset', marginTop: 8 }}>
                        <Form.Item style={{ marginBottom: 0, width: 80 }} name={'cycleType'}>
                          <Select allowClear={false}>
                            {cycleTypeOpts.map((c) => (
                              <Select.Option value={c.value} key={c.value}>
                                每{c.label}
                              </Select.Option>
                            ))}
                          </Select>
                        </Form.Item>
                        {renderCycleTypeVal()}
                        <Form.Item style={{ marginBottom: 0, width: 80 }} name={'cycleTime'}>
                          <TimePicker allowClear={false} format={'HH:mm'} />
                        </Form.Item>
                      </Input.Group>
                    </>
                  )}
                </div>
              </>
            )}
          </>
        )}
      </Form>
    </div>
  );
};

export default MessageConfig;
