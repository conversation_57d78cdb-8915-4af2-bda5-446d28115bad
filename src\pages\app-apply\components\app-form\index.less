.p10 {
  padding: 10px;
}

.container {
  height: 100%;
  display: flex;
  overflow: auto;
  background: #fff;

  :global {
    .ant-form-item {
      .ant-form-item-label {
        > label {
          width: 100%;

          &::after {
            display: none;
          }
        }
      }
    }
  }
}

.left {
  flex-shrink: 0;
  // width: 260px;
  width: 528px;
  // border-right: 1px solid #c9cdd4;
  box-shadow: 2px 0 6px 0 rgba(0, 0, 0, 10%);
  overflow: auto;
  display: flex;
  flex-direction: column;

  .leftHeader {
    flex-shrink: 0;
    height: 40px;
    padding: 0 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .groupTittle {
    font-size: 14px;
    color: #1d2129;
    font-weight: bold;
  }

  .leftContent {
    flex-grow: 1;
    overflow: auto;
    // padding: 10px;
    padding: 12px;

    .returnWrapper {
      margin-bottom: 20px;

      .returnTitle {
        font-weight: bold;
        color: #1d2129;
      }

      .returnContent {
        word-break: break-all;
        color: #4e5969;
      }
    }

    .formItemWrapper {
      position: relative;

      .labelWrapper {
        flex-grow: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 8px;
      }
    }
  }

  .leftFooter {
    flex-shrink: 0;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    box-shadow: 2px 0 6px 0 rgba(0, 0, 0, 10%);
    gap: 10px;
  }
}

.right {
  flex-grow: 1;
}

.dragBtn {
  width: 12px;
  height: 36px;
  color: #737373;
  background: #f2f3f5;
  // border-radius: 0 50px 50px 0;
  border: 1px solid #c9cdd4;
  display: flex;
  align-items: center;
  justify-content: space-evenly;

  .btnLine {
    width: 1px;
    height: 20px;
    background: #c9cdd4;
    display: inline-block;
  }
}
