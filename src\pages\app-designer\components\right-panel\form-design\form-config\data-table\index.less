.container {
  position: relative;
  padding: 4px 7px;
  border-radius: 4px;
  border: 1px solid #c9cdd4;

  &.selected {
    border: 2px solid #4d7bf6;
  }
  &.noBorder {
    border: none;
  }
}

.title {
  font-weight: bold;
}

.tableWrapper {
  margin-top: 10px;

  :global {
    .ant-table-thead {
      tr {
        .ant-table-cell {
          padding: 0;
          font-weight: normal;
          min-height: 40px;
        }
      }
    }

    .ant-table-tbody {
      display: none;
    }
  }
}

.table {
  position: relative;
  display: flex;
  overflow: auto;
  padding-top: 20px;
  min-height: 60px;
}

.tableInner {
  position: relative;
  width: 100%;
  display: flex;

  .headerBg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 38px;
    background-color: #deecfc;
    z-index: 0;
  }
}

.column {
  position: relative;
  flex-shrink: 0;
  // width: 150px;
  min-width: 150px;
  display: flex;
  flex-direction: column;
}

.headerCell {
  height: 40px;
  padding: 0 4px;
  background-color: #deecfc;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 2px dashed transparent;

  &.selected {
    border: 2px dashed #4d7bf6;
  }

  .name {
    overflow: auto;
  }

  .readonly {
    flex-shrink: 0;
    margin: 0 0 0 8px;
  }
}

.statisticsItem {
  height: 40px;
  padding: 0 15px;
  overflow: auto;
  display: flex;
  align-items: center;
}
