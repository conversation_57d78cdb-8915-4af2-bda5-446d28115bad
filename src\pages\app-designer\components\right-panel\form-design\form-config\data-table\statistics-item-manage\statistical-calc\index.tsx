import { ExpressionType } from '@/types/statistic-item';
import { CloseCircleFilled } from '@ant-design/icons';
import { Col, Form, FormListFieldData, Row, Select } from 'antd';
import { memo } from 'react';
import { CALC_FIELD_FUNC_OPTIONS, CalcList } from '../const';
import FormFieldSelect from './form-field-select';

interface IProps {
  remove: (index: number | number[]) => void;
  prefixName: string[];
  field: FormListFieldData;
  index: number;
  formFields: any[];
  isViewMode?: boolean;
}

const StatisticalCalcItem = memo(({ remove, prefixName, field, index, formFields, isViewMode = false }: IProps) => {
  return (
    <>
      <Row gutter={12}>
        <Form.Item hidden name={[field.name, 'expressionType']} initialValue={index === 0 ? ExpressionType.Func : ExpressionType.Symbol} />
        {index !== 0 && (
          <Col span={4}>
            <Form.Item name={[field.name, 'value']} rules={[{ required: true, message: '请选择' }]}>
              <Select options={CalcList} />
            </Form.Item>
          </Col>
        )}
        <Col span={index !== 0 ? 8 : 12}>
          <Form.Item name={[field.name, 'item']} rules={[{ required: true, message: '请选择' }]}>
            <FormFieldSelect formFields={formFields} />
          </Form.Item>
        </Col>
        <Col span={11}>
          <Form.Item name={[field.name, 'functionType']} rules={[{ required: true, message: '请选择' }]}>
            <Select options={CALC_FIELD_FUNC_OPTIONS} placeholder="请选择" />
          </Form.Item>
        </Col>
        {index !== 0 && !isViewMode && (
          <Col span={1}>
            <Form.Item>
              <CloseCircleFilled style={{ color: '#C9CDD4' }} onClick={() => remove(field.name)} />
            </Form.Item>
          </Col>
        )}
      </Row>
    </>
  );
});

export default StatisticalCalcItem;
