import { fieldAuth, getFieldAuthList, getFieldAuthPostList } from '@/services/calc-field';
import { CalcFieldVO } from '@/types/calc-field';
import { Post } from '@/types/post';
import { Avatar, Checkbox, Empty, Input, Tree } from '@gwy/components-web';
import classNames from 'classnames';
import { cloneDeep, isEmpty } from 'lodash';
import { forwardRef, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import FieldItem from '../../field-item';
import { TabType } from '../../my-create-wrap';
import styles from './index.less';

export type CustomAuthRef = {
  handleSubmit: () => Promise<any>;
};

interface IProps {
  postId?: string;
  type?: TabType;
}

const CustomAuth = forwardRef<CustomAuthRef, IProps>(({ postId, type }, ref) => {
  const [orgList, setOrgList] = useState([]);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [calcList, setCalcList] = useState<CalcFieldVO[]>([]);
  const [selectedPost, setSelectedPost] = useState<Post>(null);
  const [postFieldAuths, setPostFieldAuths] = useState<Array<{ postId?: string; fieldIds?: string[] }>>([]);

  useEffect(() => {
    (async () => {
      try {
        const [calcList, org] = await Promise.all([getFieldAuthList(postId, postId), getFieldAuthPostList(postId)]);
        setCalcList(calcList || []);
        setOrgList([org]);
      } catch (e) {}
    })();
  }, [postId]);

  const handleSubmit = () => {
    return fieldAuth({
      operatePostId: postId,
      postFieldAuths,
    });
  };

  useImperativeHandle(ref, () => ({
    handleSubmit,
  }));

  // 处理检索
  const handleDepSearch = (value) => {
    setSearchKeyword(value);
  };

  const handleList = useMemo(() => {
    if (!searchKeyword) {
      return orgList;
    }

    const memoData = cloneDeep(orgList);
    let searchList = [];
    const loopSearchData = (originData, prevLevel = {}) => {
      originData = originData
        ?.map((firstLevelItem) => {
          if (
            (firstLevelItem?.orgName || '').includes(searchKeyword) ||
            (firstLevelItem?.deptName || '').includes(searchKeyword) ||
            (firstLevelItem?.postName || '').includes(searchKeyword) ||
            (firstLevelItem?.userName || '').includes(searchKeyword)
          ) {
            return {
              ...firstLevelItem,
              hidden: false,
            };
          }

          const prev = Object.keys(prevLevel).length > 0 ? prevLevel : firstLevelItem;
          const prevHasInclude =
            (prev?.orgName || '').includes(searchKeyword) ||
            (prev?.deptName || '').includes(searchKeyword) ||
            (prev?.postName || '').includes(searchKeyword) ||
            (prev?.userName || '').includes(searchKeyword);
          if (!prevHasInclude && (!isEmpty(firstLevelItem?.depts) || !isEmpty(firstLevelItem?.posts))) {
            firstLevelItem.depts = loopSearchData(firstLevelItem.depts, firstLevelItem);
            firstLevelItem.posts = loopSearchData(firstLevelItem.posts, firstLevelItem);
            const everyHidden = firstLevelItem.depts?.every((item) => item.hidden) && firstLevelItem.posts?.every((item) => item.hidden);
            return {
              ...firstLevelItem,
              hidden: everyHidden,
            };
          }
          return {
            ...firstLevelItem,
            hidden: true,
          };
        })
        .filter(Boolean);

      return originData;
    };
    searchList = loopSearchData(memoData);

    return searchList;
  }, [searchKeyword, orgList]);

  return (
    <div className={styles.container}>
      <div className={styles.leftContainer}>
        <div className={styles.search}>
          <Input.Search
            placeholder="搜索"
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleDepSearch((e.target as HTMLInputElement).value);
              }
            }}
            allowClear
          />
        </div>
        <div className={styles.postList}>
          {isEmpty(handleList) ? (
            <Empty />
          ) : (
            <>
              <Tree
                rootStyle={{
                  backgroundColor: 'transparent',
                }}
                style={{ padding: '10px 0' }}
                defaultExpandAll
                selectable
              >
                {handleList.map((org) => {
                  const renderDepts = (depts) => {
                    return depts?.map((dept) => (
                      <Tree.TreeNode
                        style={{
                          fontWeight: 'bold',
                          display: dept?.hidden ? 'none' : 'inline-flex',
                        }}
                        key={org.deptId}
                        title={`${dept.deptName}（${dept.posts?.length || 0}）`}
                      >
                        {dept.posts?.map((post) => (
                          <Tree.TreeNode
                            key={post.postId}
                            className={styles.postItemBox}
                            style={{
                              display: post?.hidden ? 'none' : 'inline-flex',
                            }}
                            title={
                              <div
                                className={classNames(styles.postItem, {
                                  [styles.active]: selectedPost?.postId === post.postId,
                                })}
                                onClick={async () => {
                                  setSelectedPost(post);
                                  const requested = postFieldAuths.some((item) => item.postId === post.postId);
                                  if (!requested) {
                                    const fields = await getFieldAuthList(postId, post.postId);
                                    setPostFieldAuths((prev) => [
                                      ...prev,
                                      { postId: post.postId, fieldIds: fields.filter((f) => f.ifSelect).map((item) => item.fieldId) },
                                    ]);
                                  }
                                }}
                              >
                                <Avatar name={post.userName} src={post.portraitUrl} size={40} sex={post.sex} />
                                <div className={styles.detail}>
                                  <div className={styles.userName}>{post.userName || '-'}</div>
                                  <div className={styles.postName}>{post.postName || '-'}</div>
                                </div>
                              </div>
                            }
                          />
                        ))}
                        {dept.depts?.length > 0 && renderDepts(dept.depts)}
                      </Tree.TreeNode>
                    ));
                  };

                  return (
                    <Tree.TreeNode
                      style={{ fontWeight: 'bold', display: org?.hidden ? 'none' : 'inline-flex' }}
                      key={org.orgId}
                      title={`${org.orgName}（${org.depts?.length || 0}）`}
                    >
                      {renderDepts(org.depts)}
                    </Tree.TreeNode>
                  );
                })}
              </Tree>
            </>
          )}
        </div>
      </div>

      <div className={styles.rightContainer}>
        {selectedPost && (
          <>
            <Checkbox
              checked={selectedPost && postFieldAuths.find((p) => p.postId === selectedPost?.postId)?.fieldIds.length === calcList.length}
              indeterminate={
                postFieldAuths.find((p) => p.postId === selectedPost?.postId)?.fieldIds.length &&
                postFieldAuths.find((p) => p.postId === selectedPost?.postId)?.fieldIds.length !== calcList.length
              }
              onChange={(e) => {
                const { checked } = e.target;
                const checkedList = checked ? calcList.map((item) => item.fieldId) : [];
                setPostFieldAuths((pre) => {
                  return pre.map((p) => {
                    if (p.postId === selectedPost?.postId) {
                      return {
                        ...p,
                        fieldIds: checkedList,
                      };
                    }
                    return p;
                  });
                });
              }}
              style={{ marginBottom: 8 }}
            >
              全选
            </Checkbox>
            <Checkbox.Group
              value={postFieldAuths.find((p) => p.postId === selectedPost?.postId)?.fieldIds}
              onChange={(value) => {
                setPostFieldAuths((prev) => {
                  return prev.map((p) => {
                    if (p.postId === selectedPost?.postId) {
                      return {
                        ...p,
                        fieldIds: value,
                      };
                    }
                    return p;
                  });
                });
              }}
              style={{ width: '100%' }}
            >
              <div className={styles.fieldList}>
                {calcList.map((field, i) => (
                  <div key={i} className={styles.field}>
                    <FieldItem field={field} />
                  </div>
                ))}
              </div>
            </Checkbox.Group>
          </>
        )}
      </div>
    </div>
  );
});

export default CustomAuth;
