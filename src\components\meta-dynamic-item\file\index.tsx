import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { getFileIconByUrl } from '@/utils/metadata';
import { Upload } from '@gwy/components-web';
import { memo, useMemo } from 'react';
import { UseIn } from '../const';
import styles from './index.less';

export type FileMetaProps = {
  useIn?: UseIn;
  tag?: DataUnitTagVO;
  field?: FormField;
  value?: any;
  isPreview?: boolean;
  onChange?: (value) => void;
};

const FileMeta = memo(({ useIn, tag, field, value, onChange }: FileMetaProps) => {
  const { placeholder, readonly } = field || {};
  const { metaDataFileDTO } = field?.config || {};
  const { formats, sizeUnit, sizeMax, fileNum, ifLimitFormat } = metaDataFileDTO || {};

  const accepts = useMemo(() => (Array.isArray(formats) ? formats.map((f) => f.toLowerCase()) : []), [formats]);

  const linkDesc = useMemo(
    () =>
      [ifLimitFormat ? `格式支持${accepts.join('、')}` : '格式不限', `最多上传${fileNum}个`, `单文件大小${sizeMax}${sizeUnit}以内`]
        .filter(Boolean)
        .join('，'),
    [ifLimitFormat, accepts, fileNum, sizeMax, sizeUnit],
  );

  if (readonly) {
    if (useIn === UseIn.Designer) {
      return '回显数据';
    }
    const { data: tagData } = value || {};
    if (!tagData) return '-';
    const files = Array.isArray(tagData) ? tagData : [tagData];
    return (
      <div className={styles.fileWrapper}>
        {files.map((file, idx) => {
          const { url, name } = file;
          const FileIcon = getFileIconByUrl(url);
          return (
            <div key={idx} className={styles.fileItem}>
              <img src={FileIcon} alt={name} />
              <a href={url} target="_blank" rel="noreferrer">
                {name}
              </a>
            </div>
          );
        })}
      </div>
    );
  }
  const fileSizeUnit: any = sizeUnit.slice(0, 1);

  return (
    <Upload
      fileList={value}
      maxTotal={fileNum}
      accept={accepts}
      format="object"
      fileSizeUnit={fileSizeUnit}
      fileSize={sizeMax}
      linkDesc={placeholder || linkDesc}
      onChange={(v) => {
        setTimeout(() => {
          onChange(v);
        }, 200);
      }}
      listType="file"
      className={styles.uploadContainer}
      btnText="上传文件"
      multiple
    />
  );
});

export default FileMeta;
