import { AppDesignerContext } from '@/pages/app-designer/context';
import { CalcFieldVO } from '@/types/calc-field';
import { Form } from 'antd';
import { memo, useContext, useEffect } from 'react';
import { FieldCreateContext } from '../../field-create-modal/context';
import FormItemCondition from '../components/form-item-condition';
import styles from '../index.less';

interface IProps {
  calcField: CalcFieldVO;
}

const CalcCalcDesc = memo<IProps>(({ calcField }) => {
  const { post, configuredDataUnits, allDataUnitsWithTags, calcFields } = useContext(AppDesignerContext);
  const [form] = Form.useForm();
  const { setFieldsValue } = form;

  useEffect(() => {
    if (calcField) {
      setFieldsValue({
        ...calcField,
        createPostId: post.postId,
      });
    }
  }, [calcField, setFieldsValue, post.postId]);

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <FieldCreateContext.Provider
          value={{
            configuredDataUnits,
            allDataUnitsWithTags,
            fieldList: calcFields,
            fieldType: calcField.fieldType,
          }}
        >
          <Form form={form}>
            <FormItemCondition disabled />
          </Form>
        </FieldCreateContext.Provider>
      </div>
    </div>
  );
});

export default CalcCalcDesc;
