import { getMetaTagUniqueId } from '@/utils/metadata';
import { Table } from '@gwy/components-web';
import { memo } from 'react';

const Summary = memo<{
  columns: any[];
  statisticalItems?: any;
}>((props) => {
  const { columns, statisticalItems } = props;
  return (
    <Table.Summary.Row>
      <Table.Summary.Cell index={1}>统计项</Table.Summary.Cell>
      {columns.map((col, index) => {
        const columnId = getMetaTagUniqueId(col);
        const statItem = statisticalItems?.find((item) => item.columnId === columnId);
        return (
          <Table.Summary.Cell key={index} index={index}>
            {statItem ? `${Number(statItem.statValue).toFixed(2)}` : '-'}
          </Table.Summary.Cell>
        );
      })}
    </Table.Summary.Row>
  );
});

export default Summary;
