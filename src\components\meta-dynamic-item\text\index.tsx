import { TextInputType } from '@/const/metadata';
import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { dispatchDynamicParamChange } from '@/utils/event-emitter';
import { InputProps, Select } from '@gwy/components-web';
import { memo, useCallback } from 'react';
import { UseIn } from '../const';
import { WidgetType } from './const';
import TextEnumMeta from './enum';
import TextManualMeta from './manual';

const TextMeta = memo<
  {
    useIn?: UseIn;
    // 展示类型
    displayType?: WidgetType;
    tag?: DataUnitTagVO;
    field?: FormField;
    value?: any;
    onChange?: (value) => void;
    isPreview?: boolean;
  } & Pick<InputProps, 'maxLength' | 'placeholder'>
>((props) => {
  const { useIn, tag, field, value, onChange, isPreview } = props;

  const { inputType: metaInputType } = tag.tagMetaDataConfig?.metaDataTextDTO || {};
  const { inputType, enumList } = field?.config?.metaDataTextDTO || {};

  const handleChange = useCallback(
    (value) => {
      typeof onChange === 'function' && onChange(value);
      dispatchDynamicParamChange({ dataUnitId: tag.dataUnitId, tagId: tag.tagId, value });
    },
    [onChange, tag],
  );
  const render = () => {
    if (metaInputType === TextInputType.enum) {
      return <TextEnumMeta useIn={useIn} tag={tag} field={field} value={value} onChange={handleChange} isPreview={isPreview} />;
    }
    if (metaInputType === TextInputType.manual) {
      if (inputType === TextInputType.enum) {
        <Select options={(enumList as any)?.map((ite) => ({ value: ite, label: ite }))} value={value} onChange={handleChange} />;
      }
      return <TextManualMeta useIn={useIn} tag={tag} field={field} value={value} onChange={handleChange} isPreview={isPreview} />;
    }
  };

  return render();
});

export default TextMeta;
