import { MetaTagType } from '@/const/metadata';
import { SelectedStateType } from '@/pages/app-designer/const';
import { AppDesignerContext } from '@/pages/app-designer/context';
import { FormField, SourceType } from '@/types/form-field';
import { getCalcFieldDisplayName, getMetaTagUniqueId } from '@/utils/metadata';
import { DeleteOutlined, InfoCircleOutlined, RetweetOutlined } from '@ant-design/icons';
import { Table, TableColumnsType, Tag, TextEllipsisTooltip, Tooltip } from '@gwy/components-web';
import classNames from 'classnames';
import { produce } from 'immer';
import { isEmpty } from 'lodash-es';
import { useContext, useMemo } from 'react';
import { validateFormFields } from '../../utils';
import styles from './index.less';

export type DataTableProps = {
  onClick?: () => void;
  onDelete?: () => void;
  selected?: boolean;
  columnSelectable?: boolean;
  type?: 'combineTag' | 'table';
  tableId?: string;
  onDeleteColumn?: (field: FormField) => void;
  noBorder?: boolean;
};

const DataTable = ({ onClick, onDelete, selected, columnSelectable = true, type, tableId, onDeleteColumn, noBorder }: DataTableProps) => {
  const { tagsMap, combineTagConfig, tableList, setTableList, selectedState, setSelectedState, getPropertyConfigRef } =
    useContext(AppDesignerContext);

  // 是否全部是输出
  const isAllOutput = useMemo(() => {
    if (tableList?.find((t) => t.id === tableId)?.fields?.some((field) => field.sourceType === SourceType.Tag && !field.readonly)) {
      return false;
    }
    return true;
  }, [tableList, tableId]);

  // 切换输入输出
  const toggleIO = () => {
    setTableList(
      produce(tableList, (draft) => {
        const t = draft.find((t) => t.id === tableId);
        t?.fields?.forEach((field) => {
          const tag = tagsMap[getMetaTagUniqueId(field)];
          if (field.sourceType === SourceType.Tag && tag?.type !== MetaTagType.System) {
            field.readonly = !isAllOutput;
          }
        });
      }),
    );
  };

  const fields = useMemo(() => {
    if (type === 'combineTag') {
      return combineTagConfig?.fields || [];
    } else if (type === 'table') {
      return tableList?.find((t) => t.id === tableId)?.fields || [];
    }
  }, [type, combineTagConfig, tableList, tableId]);

  const renderColumns = (): TableColumnsType<FormField> => {
    return fields?.map((field) => {
      const uniqId = getMetaTagUniqueId(field);
      const tag = tagsMap[uniqId];
      const columnSelected =
        columnSelectable &&
        ((type === 'combineTag' && selectedState.type === SelectedStateType.combineTagItem && selectedState.formItemId === uniqId) ||
          (type === 'table' &&
            selectedState.type === SelectedStateType.tableItem &&
            selectedState.formItemId === uniqId &&
            selectedState.tableId === tableId));
      return {
        key: uniqId,
        dataIndex: uniqId,
        width: 150,
        title: (
          <div key={uniqId} className={styles.column}>
            {/* 标签、字段 */}
            <div
              className={classNames(styles.headerCell, {
                [styles.selected]: columnSelected,
              })}
              onClick={async (e) => {
                e.stopPropagation();
                await validateFormFields(getPropertyConfigRef());

                if (type === 'combineTag') {
                  setSelectedState({
                    type: SelectedStateType.combineTagItem,
                    formItemId: uniqId,
                    formItemType: field.sourceType,
                  });
                } else if (type === 'table') {
                  setSelectedState({
                    type: SelectedStateType.tableItem,
                    formItemId: uniqId,
                    formItemType: field.sourceType,
                    tableId,
                  });
                }
              }}
            >
              {field.sourceType === SourceType.Tag && (
                <>
                  <span className={styles.name}>
                    <TextEllipsisTooltip
                      text={
                        <span>
                          {field.fieldName || tag?.name}
                          {field.fieldName && (
                            <Tooltip title={`原标签名称：${tag?.name}`}>
                              <InfoCircleOutlined style={{ marginLeft: 5, color: '#4D7BF6' }} />
                            </Tooltip>
                          )}
                        </span>
                      }
                    />
                  </span>
                  <Tag className={styles.readonly} color={field.readonly ? 'orange' : 'blue'}>
                    {field.readonly ? '输出' : '输入'}
                  </Tag>
                </>
              )}
              {field.sourceType === SourceType.Field && (
                <>
                  <span className={styles.name}>
                    {getCalcFieldDisplayName(field)}
                    {field.fieldConfig?.formFieldConfig?.fieldOtherName && (
                      <Tooltip title={`原名称：${field.fieldConfig?.fieldName}`}>
                        <InfoCircleOutlined style={{ marginLeft: 5, color: '#4D7BF6' }} />
                      </Tooltip>
                    )}
                  </span>
                  <Tag className={styles.readonly} color="orange">
                    输出
                  </Tag>
                </>
              )}
            </div>

            {/* 统计项 */}
            {/* <div className={styles.statisticsItem}>aaaa</div> */}

            {/* 删除列 */}
            {columnSelected && (
              <DeleteOutlined
                style={{ position: 'absolute', right: 2, top: 2, color: '#4d7bf6' }}
                onClick={(e) => {
                  e.stopPropagation();
                  onDeleteColumn?.(field);
                }}
              />
            )}
          </div>
        ),
        render: () => null,
      };
    });
  };
  return (
    <div
      className={classNames(styles.container, {
        [styles.selected]: selected,
        [styles.noBorder]: noBorder,
      })}
      onClick={(e) => {
        e.stopPropagation();
        onClick?.();
      }}
    >
      <div className={styles.title}>
        {type === 'combineTag' ? combineTagConfig?.combineTagName || `数据表格` : tableList?.find((t) => t.id === tableId)?.tableName || `数据表格`}
        {type === 'combineTag' && '（组合标签）'}
      </div>

      <div className={styles.tableWrapper}>
        {!isEmpty(fields) && (
          <Table
            bordered
            columns={renderColumns()}
            dataSource={[{}]}
            scroll={{ x: 'max-content' }}
            pagination={false}
            summary={
              type === 'combineTag'
                ? undefined
                : (pageData) => {
                    const statisticList = tableList?.find((t) => t.id === tableId)?.statisticList;
                    if (isEmpty(statisticList)) return null;
                    return (
                      <>
                        <Table.Summary.Row>
                          {fields.map((field, index) => {
                            const statisticalItem = statisticList?.find(
                              (s) =>
                                s.location &&
                                ((s.location.sourceType === SourceType.Tag &&
                                  s.location.dataUnitId === field.dataUnitId &&
                                  s.location.sourceId === field.sourceId) ||
                                  (s.location.sourceType === SourceType.Field && s.location.sourceId === field.fieldConfig?.fieldId)),
                            );
                            return (
                              <Table.Summary.Cell key={index} index={index}>
                                <div
                                  style={{ textAlign: 'center' }}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                  }}
                                >
                                  {statisticalItem?.statisticName}
                                </div>
                              </Table.Summary.Cell>
                            );
                          })}
                        </Table.Summary.Row>
                      </>
                    );
                  }
            }
          />
        )}
        {isEmpty(fields) && <div style={{ backgroundColor: '#deecfc', height: 40 }} />}
      </div>

      {/* <div className={styles.table}>
        <div className={styles.tableInner}>
          <div className={styles.headerBg} />

          {fields?.map((field) => {
            const uniqId = getMetaTagUniqueId(field);
            const tag = tagsMap[uniqId];
            const columnSelected =
              columnSelectable &&
              ((type === 'combineTag' && selectedState.type === SelectedStateType.combineTagItem && selectedState.formItemId === uniqId) ||
                (type === 'table' &&
                  selectedState.type === SelectedStateType.tableItem &&
                  selectedState.formItemId === uniqId &&
                  selectedState.tableId === tableId));
            return (
              <div key={uniqId} className={styles.column}>
                <div
                  className={classNames(styles.headerCell, {
                    [styles.selected]: columnSelected,
                  })}
                  onClick={(e) => {
                    e.stopPropagation();
                    if (type === 'combineTag') {
                      setSelectedState({
                        type: SelectedStateType.combineTagItem,
                        formItemId: uniqId,
                        formItemType: field.sourceType,
                      });
                    } else if (type === 'table') {
                      setSelectedState({
                        type: SelectedStateType.tableItem,
                        formItemId: uniqId,
                        formItemType: field.sourceType,
                        tableId,
                      });
                    }
                  }}
                >
                  {field.sourceType === SourceType.Tag && (
                    <>
                      <span className={styles.name}>
                        <TextEllipsisTooltip text={tag?.name} />
                      </span>
                      <Tag className={styles.readonly} color={field.readonly ? 'orange' : 'blue'}>
                        {field.readonly ? '输出' : '输入'}
                      </Tag>
                    </>
                  )}
                  {field.sourceType === SourceType.Field && (
                    <>
                      <span className={styles.name}> {field.fieldConfig?.fieldName}</span>
                      <Tag className={styles.readonly} color="orange">
                        输出
                      </Tag>
                    </>
                  )}
                </div>

                <div className={styles.statisticsItem}>aaaa</div>

                {columnSelected && (
                  <DeleteOutlined
                    style={{ position: 'absolute', right: 0, top: -18, color: '#4d7bf6' }}
                    onClick={(e) => {
                      e.stopPropagation();
                      onDeleteColumn?.(field);
                    }}
                  />
                )}
              </div>
            );
          })}
        </div>
      </div> */}

      {/* 删除表格 */}
      {selected && (
        <div style={{ position: 'absolute', right: 4, top: 4, color: '#4d7bf6', display: 'flex', gap: 8 }}>
          {type === 'table' && (
            <RetweetOutlined
              onClick={() => {
                toggleIO();
              }}
            />
          )}
          <DeleteOutlined
            onClick={(e) => {
              e.stopPropagation();
              onDelete?.();
            }}
          />
        </div>
      )}
    </div>
  );
};

export default DataTable;
