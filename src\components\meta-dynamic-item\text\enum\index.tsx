import { EnumType } from '@/const/metadata';
import { datasourceAPI } from '@/services';
import { FormField } from '@/types/form-field';
import { MetaDataTextEnumDTO } from '@/types/metadata';
import { DataUnitTagVO } from '@/types/tag';
import { cloneDeep } from 'lodash-es';
import { memo, useEffect, useState } from 'react';
import { UseIn } from '../../const';
import CustomTreeSelect, { fieldNames } from './custom-tree-select';

// 过滤包含/排除的选项
const loop = (list, enumListLimit: MetaDataTextEnumDTO[], appoint, enumType) => {
  const idKey = fieldNames[enumType]?.value;
  const childrenKey = fieldNames[enumType]?.children;
  return list.filter((item) => {
    let subList = [];
    if (item[childrenKey] && item[childrenKey].length) {
      item.selectable = false;
      subList = loop(item[childrenKey], enumListLimit, appoint, enumType);
    }

    let match = enumListLimit?.some((a) => a.id === item[idKey]);
    if (!appoint) {
      match = !match;
      if (match && Array.isArray(item[childrenKey]) && item[childrenKey].length && !subList.length) {
        match = !match;
      }
    }
    item[childrenKey] = subList;
    return match || (subList && subList.length);
  });
};

const TextEnumMeta = memo<{
  useIn?: UseIn;
  tag?: DataUnitTagVO;
  field?: FormField;
  value?: any;
  onChange?: (value) => void;
  isPreview?: boolean;
}>(({ useIn, tag, field, value, onChange, isPreview }) => {
  const { enumList: metaEnumList, enumType, dictCode } = tag.tagMetaDataConfig?.metaDataTextDTO || {};
  const { placeholder } = field || {};
  const { multipleChoice, appoint } = field?.config || {};
  const { enumList: enumListLimit } = field?.config?.metaDataTextDTO || {};

  const [enumList, setEnumList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [initialized, setInitialized] = useState(false);

  useEffect(() => {
    if (enumType === EnumType.general) {
      setLoading(true);
      try {
        datasourceAPI.getDictTreeDataByCode(dictCode).then((res) => {
          setEnumList(loop(res || [], enumListLimit, appoint, enumType));
          setInitialized(true);
        });
      } catch (error) {}
      setLoading(false);
    } else {
      setEnumList(loop(cloneDeep(metaEnumList || []), enumListLimit, appoint, enumType));
      setInitialized(true);
    }
  }, [metaEnumList, enumListLimit, appoint, enumType, dictCode]);

  // 选项设置改动，需要过滤掉无效的默认值
  useEffect(() => {
    if (!initialized) {
      return;
    }
    if (useIn === UseIn.DefaultValueSetting) {
      const idKey = fieldNames[enumType]?.value;
      const childrenKey = fieldNames[enumType]?.children;
      const validIds = [];
      const _loop = (list) => {
        list.forEach((item) => {
          validIds.push(item[idKey]);
          if (item[childrenKey]) {
            _loop(item[childrenKey]);
          }
        });
      };
      _loop(enumList);
      const _value = value?.filter((item) => validIds.some((id) => id === item.id));
      onChange(_value);
    }
  }, [enumList, initialized]);

  if (field?.readonly || isPreview) {
    if (useIn === UseIn.Designer) {
      return '回显数据';
    }
    const { value: tagValue, data: tagData } = value || {};
    return tagValue ?? '-';
  }

  return (
    <CustomTreeSelect
      enumType={enumType}
      multiple={multipleChoice}
      treeData={enumList}
      loading={loading}
      value={value}
      onChange={onChange}
      style={{ width: '100%' }}
      placeholder={placeholder || '请选择'}
    />
  );
});

export default TextEnumMeta;
