import MetaDynamicItem from '@/components/meta-dynamic-item';
import { UseIn } from '@/components/meta-dynamic-item/const';
import { EditorTargetType } from '@/components/onlyoffice-editor/const';
import { SourceType } from '@/types/form-field';
import { getMetaTagUniqueId } from '@/utils/metadata';
import { InfoCircleOutlined } from '@ant-design/icons';
import { Empty, Form, Radio, Tag } from '@gwy/components-web';
import classNames from 'classnames';
import { isEmpty } from 'lodash-es';
import { useContext } from 'react';
import { AppDesignerContext } from '../../../context';
import DataTable from '../../right-panel/form-design/form-config/data-table';
import SubFormTabs from '../../sub-form-tabs';
import styles from './index.less';

const WordDesignMaterials = () => {
  const { tagsMap, configuredFields, editorRef, combineTagConfig, tableList, wordReadonly, setWordReadonly } = useContext(AppDesignerContext);

  const renderItems = () => {
    return configuredFields.map((field) => {
      const uniqueId = getMetaTagUniqueId(field);
      const targetId = uniqueId;
      let targetType;
      let targetName;
      if (field.sourceType === SourceType.Tag) {
        targetType = EditorTargetType.tag;
        targetName = field.fieldName || tagsMap[uniqueId]?.name;
      } else if (field.sourceType === SourceType.Field) {
        targetType = EditorTargetType.field;
        targetName = field.fieldConfig?.formFieldConfig?.fieldOtherName || field.fieldConfig?.fieldName;
      }

      // 标签
      const renderTag = () => {
        const tag = tagsMap[getMetaTagUniqueId(field)];
        if (!tag) {
          return null;
        }
        return (
          <>
            <Form.Item hidden name={[uniqueId, 'id']} initialValue={field.sourceId} />
            {/* name 方便调试 */}
            <Form.Item hidden name={[uniqueId, 'name']} initialValue={tag.name} />

            <Form.Item hidden name={[uniqueId, 'dataUnitId']} initialValue={field.dataUnitId} />
            <MetaDynamicItem useIn={UseIn.Designer} tag={tag} field={field} />
          </>
        );
      };

      // 计算字段
      const renderCalcField = () => {
        return (
          <>
            <Form.Item
              label={
                <div className={styles.labelWrapper}>
                  <span>{field.fieldConfig?.fieldName}</span>
                  <Tag color="orange" style={{ margin: 0 }}>
                    输出
                  </Tag>
                </div>
              }
            >
              <span>回显数据</span>
            </Form.Item>
          </>
        );
      };

      return (
        <div
          key={uniqueId}
          className={classNames(styles.formItemWrapper)}
          style={{
            width: (field.sourceType === SourceType.Tag ? field.widgetWith : field.fieldConfig?.formFieldConfig?.widgetWith) ?? '50%',
          }}
          draggable
          onDragEnd={(e) => {
            editorRef.current?.updateDocEditor(
              e,
              {
                targetType,
                targetId,
                targetName,
              },
              (controlTag) => {
                console.log('tag added', controlTag);
              },
            );
          }}
        >
          {field.sourceType === SourceType.Tag && renderTag()}
          {field.sourceType === SourceType.Field && renderCalcField()}
        </div>
      );
    });
  };

  const noConfigs = isEmpty(configuredFields) && isEmpty(combineTagConfig) && isEmpty(tableList);

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <InfoCircleOutlined style={{ color: '#4D7BF6', marginRight: 6 }} /> 可拖动标签置右侧Word中使用
      </div>

      <div className={styles.contentWrapper}>
        <div className={styles.subFormTabs}>
          <SubFormTabs />
        </div>
        <div className={styles.content}>
          {!noConfigs && (
            <>
              {/* 基础分组 */}
              <div>
                <div className={styles.title}>基础分组</div>
                <Form layout="vertical" colon={false}>
                  <div className={styles.formItemList}>{renderItems()}</div>
                </Form>

                {/* 组合标签 */}
                {combineTagConfig && (
                  <div
                    draggable
                    onDragEnd={(e) => {
                      editorRef.current?.updateDocText(`{{#${combineTagConfig.id}}}`);
                    }}
                  >
                    <div className={styles.combineTagWrapper}>
                      <DataTable noBorder type="combineTag" columnSelectable={false} />
                    </div>
                  </div>
                )}
              </div>

              {/* 数据表格 */}
              <div className={styles.dataTableList}>
                {tableList?.map((table, i) => {
                  return (
                    <div
                      key={table.id}
                      draggable
                      onDragEnd={(e) => {
                        editorRef.current?.updateDocText(`{{#${table.id}}}`);
                      }}
                    >
                      <div className={styles.dataTableWrapper}>
                        <DataTable noBorder type="table" tableId={table.id} columnSelectable={false} />
                      </div>
                    </div>
                  );
                })}
              </div>
            </>
          )}

          {noConfigs && <Empty />}
        </div>
      </div>

      <div className={styles.footer}>
        <span>Word应用模式：</span>
        <Radio.Group value={wordReadonly} onChange={(e) => setWordReadonly(e.target.value)}>
          <Radio value={true}>查看模式</Radio>
          <Radio value={false}>编辑模式</Radio>
        </Radio.Group>
      </div>
    </div>
  );
};

export default WordDesignMaterials;
