import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { getMetaTagUniqueId } from '@/utils/metadata';
import { Form } from '@gwy/components-web';
import { memo, useEffect, useMemo } from 'react';
import { UseIn } from '../const';
import UnitMeta from './index';
// import { validator } from './validator';

export type UnitMetaFormProps = {
  useIn?: UseIn;
  tag?: DataUnitTagVO;
  field?: FormField;
  label?: React.ReactNode;
  prefix?: any[];
  isPreview?: boolean;
};

const UnitMetaForm = memo<UnitMetaFormProps>(({ useIn, tag, field, label, prefix, isPreview }) => {
  const { required, config, readonly } = field || {};
  const { metaDataUnitDTO } = config || {};
  const { min, max, unit, rangeType, configList } = metaDataUnitDTO || {};
  const { type } = tag.tagMetaDataConfig.metaDataUnitDTO;

  const metaTagUniqueId = getMetaTagUniqueId(field);
  const form = Form.useFormInstance();
  //   const rules = useMemo(
  //     () =>
  //       !readonly && [UseIn.App].includes(useIn)
  //         ? [
  //             {
  //               validator: (rule, value) => validator(rule, value, required, { min, max, unit, type, rangeType }),
  //             },
  //           ].filter(Boolean)
  //         : undefined,
  //     [required, min, max, unit, type, rangeType, readonly],
  //   );

  // 制表时，中间展示区域使用此组件时，根据属性配置动态展示
  useEffect(() => {
    const value = form.getFieldValue([metaTagUniqueId, 'value']);
    if (value && value?.raw_unit && (!configList?.length || !configList.includes(value.raw_unit))) {
      form.setFieldValue([metaTagUniqueId, 'value'], undefined);
    }
  }, [configList?.length, metaTagUniqueId]);

  const initialValue = useMemo(
    () => ({
      raw_unit: configList[0],
    }),
    [],
  );

  return (
    <Form.Item
      label={label}
      name={[...(prefix || []), metaTagUniqueId, 'value']}
      rules={[
        {
          required: true,
        },
      ]}
      required={required && !readonly}
      initialValue={initialValue}
    >
      <UnitMeta useIn={useIn} tag={tag} field={field} isPreview={isPreview} />
    </Form.Item>
  );
});

export default UnitMetaForm;
