import { getExtConfig } from '@/components/data-query-setting';
import AddressMeta from '@/components/meta-dynamic-item/address';
import CodeMeta from '@/components/meta-dynamic-item/code';
import { UseIn } from '@/components/meta-dynamic-item/const';
import { getDynamicItemFormRules } from '@/components/meta-dynamic-item/data-table/utils';
import FileMeta from '@/components/meta-dynamic-item/file';
import NumberMeta from '@/components/meta-dynamic-item/number';
import ObjectMeta from '@/components/meta-dynamic-item/object';
import TextMeta from '@/components/meta-dynamic-item/text';
import TimeMeta from '@/components/meta-dynamic-item/time';
import UnitMeta from '@/components/meta-dynamic-item/unit';
import { MetaDataType } from '@/const/metadata';
import { AppDesignerContext } from '@/pages/app-designer/context';
import { FormField } from '@/types/form-field';
import { getMetaTagUniqueId } from '@/utils/metadata';
import { Form, Radio, Switch } from '@gwy/components-web';
import { InputNumber, Select } from 'antd';
import { memo, useContext, useMemo } from 'react';
import ConditionsConfig from './conditions-config';
import styles from './index.less';

export const inputDefaultConfigKey = 'inputDefaultConfig';
export const dateConfigKey = 'dateConfig';
export const systemTimeDefaultKey = 'systemTimeDefault';

export enum DefaultValueType {
  Query = 'query',
  Default = 'default_value',
}

export enum DateConfigType {
  System = 'system',
  Custom = 'custom',
}

export enum DateFormatType {
  Year = 1,
  Month = 2,
  Day = 3,
}

export enum OperatorType {
  Now = 0,
  Before = -1,
}

export type DefaultValueSettingProps = {
  field?: FormField;
};

const DefaultValueSetting = memo(({ field }: DefaultValueSettingProps) => {
  const { tagsMap } = useContext(AppDesignerContext);
  const tag = tagsMap[getMetaTagUniqueId(field)];

  const form = Form.useFormInstance();
  const open = Form.useWatch([inputDefaultConfigKey, 'open'], form);
  const type = Form.useWatch([inputDefaultConfigKey, 'type'], form);
  const dateConfigType = Form.useWatch([inputDefaultConfigKey, dateConfigKey, 'type'], form);
  const dateFormat = Form.useWatch([inputDefaultConfigKey, dateConfigKey, systemTimeDefaultKey, 'dateFormat'], form);
  const operator = Form.useWatch([inputDefaultConfigKey, dateConfigKey, systemTimeDefaultKey, 'operator'], form);

  const defaultConfigRules = useMemo(() => getDynamicItemFormRules(tag, { ...field, required: true }), [tag, field]);
  const renderItem = () => {
    const type = field?.config?.type;

    switch (type) {
      case MetaDataType.Text: {
        return <TextMeta useIn={UseIn.DefaultValueSetting} tag={tag} field={field} />;
      }
      case MetaDataType.Number: {
        return <NumberMeta useIn={UseIn.DefaultValueSetting} tag={tag} field={field} />;
      }
      case MetaDataType.DateTime: {
        return <TimeMeta useIn={UseIn.DefaultValueSetting} tag={tag} field={field} />;
      }
      case MetaDataType.Object: {
        return <ObjectMeta useIn={UseIn.DefaultValueSetting} tag={tag} field={field} />;
      }
      case MetaDataType.Unit: {
        return <UnitMeta useIn={UseIn.DefaultValueSetting} tag={tag} field={field} />;
      }
      case MetaDataType.Code: {
        return <CodeMeta useIn={UseIn.DefaultValueSetting} tag={tag} field={field} />;
      }
      case MetaDataType.File: {
        return <FileMeta useIn={UseIn.DefaultValueSetting} tag={tag} field={field} />;
      }
      case MetaDataType.Address: {
        return <AddressMeta useIn={UseIn.DefaultValueSetting} tag={tag} field={field} />;
      }
      default: {
        return null;
      }
    }
  };

  return (
    <div>
      <Form.Item label="默认数据" name={[inputDefaultConfigKey, 'open']} wrapperCol={{ style: { height: 0 } }}>
        <Switch
          style={{ position: 'absolute', top: -24, right: 0 }}
          onChange={(checked) => {
            if (checked) {
              form.setFieldValue([inputDefaultConfigKey], {
                open: true,
                type: DefaultValueType.Query,
                dataUnit: getExtConfig({ dataUnitId: tag?.dataUnitId }),
              });
            } else {
              form.setFieldValue([inputDefaultConfigKey], { open: false });
            }
          }}
        />
      </Form.Item>
      {open && (
        <div className={styles.content}>
          <Form.Item name={[inputDefaultConfigKey, 'type']} initialValue={DefaultValueType.Query}>
            <Radio.Group>
              <Radio value={DefaultValueType.Query}>数据查询</Radio>
              <Radio value={DefaultValueType.Default}>默认值</Radio>
            </Radio.Group>
          </Form.Item>

          {type === DefaultValueType.Query && (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <span>配置规则：</span>

              <Form.Item name={[inputDefaultConfigKey, 'dataUnit']} style={{ marginBottom: 0 }}>
                <ConditionsConfig tag={tag} />
              </Form.Item>
            </div>
          )}

          {type === DefaultValueType.Default && (
            <div>
              {field?.config?.type === MetaDataType.DateTime && (
                <>
                  <div style={{ display: 'flex', gap: 10 }}>
                    <Form.Item style={{ flex: 1 }} name={[inputDefaultConfigKey, dateConfigKey, 'type']} initialValue={DateConfigType.System}>
                      <Select>
                        <Select.Option value={DateConfigType.System}>系统时间</Select.Option>
                        <Select.Option value={DateConfigType.Custom}>自定义时间</Select.Option>
                      </Select>
                    </Form.Item>
                    {dateConfigType === DateConfigType.System && (
                      <Form.Item
                        style={{ flex: 1 }}
                        name={[inputDefaultConfigKey, dateConfigKey, systemTimeDefaultKey, 'dateFormat']}
                        initialValue={DateFormatType.Day}
                      >
                        <Select>
                          <Select.Option value={DateFormatType.Year}>年</Select.Option>
                          <Select.Option value={DateFormatType.Month}>年/月</Select.Option>
                          <Select.Option value={DateFormatType.Day}>年/月/日</Select.Option>
                        </Select>
                      </Form.Item>
                    )}
                  </div>
                  {dateConfigType === DateConfigType.System && (
                    <div style={{ display: 'flex', gap: 10 }}>
                      <Form.Item
                        style={{ flex: 1 }}
                        name={[inputDefaultConfigKey, dateConfigKey, systemTimeDefaultKey, 'operator']}
                        initialValue={OperatorType.Now}
                      >
                        <Select>
                          <Select.Option value={OperatorType.Now}>当日</Select.Option>
                          <Select.Option value={OperatorType.Before}>往前</Select.Option>
                        </Select>
                      </Form.Item>
                      {operator === OperatorType.Before && (
                        <Form.Item style={{ flex: 1 }} name={[inputDefaultConfigKey, dateConfigKey, systemTimeDefaultKey, 'delta']}>
                          <InputNumber addonAfter={dateFormat === DateFormatType.Year ? '年' : dateFormat === DateFormatType.Month ? '月' : '日'} />
                        </Form.Item>
                      )}
                    </div>
                  )}
                </>
              )}

              {(field?.config?.type !== MetaDataType.DateTime ||
                (field?.config?.type === MetaDataType.DateTime && dateConfigType === DateConfigType.Custom)) && (
                <Form.Item name={[inputDefaultConfigKey, 'value']} rules={defaultConfigRules}>
                  {renderItem()}
                </Form.Item>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
});

export default DefaultValueSetting;
