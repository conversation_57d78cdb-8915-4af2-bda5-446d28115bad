import { FieldType } from '@/types/calc-field';
import { DataUnitBaseVO } from '@/types/data-unit';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { ConfigProvider, Form, Modal, Radio, Select, Switch, Tooltip } from '@gwy/components-web';
import { useEffect, useMemo } from 'react';
import FilterGroup from '../filter-group';
import { DATA_QUERY_TYPE, SAME_GROUP_QUERY_TYPE } from '../filter-group/filter-const';
import GroupFilter from '../group-filter';
import SortTags from '../sort-tags';
import AddQuery from './add-query';
import styles from './index.less';

export const getExtConfig = (extConfig?: any) => {
  const { sameGroupQueryType, dataQueryType, sortTags, groupFilterNewConfig, subDataUnitIds, dataUnitId } = extConfig || {};
  return {
    sameGroupQueryType: sameGroupQueryType || SAME_GROUP_QUERY_TYPE.ONLY_SUB,
    dataQueryType: dataQueryType || DATA_QUERY_TYPE.SINGLE,
    sortTags: sortTags ? sortTags : [],
    groupFilterNewConfig: groupFilterNewConfig ? groupFilterNewConfig : null,
    subDataUnitIds: subDataUnitIds ?? [],
    dataUnitId: dataUnitId ?? null,
  };
};

interface Iprops {
  disabled?: boolean;
  fieldType?: FieldType;
  groupFlag?: boolean; // 是否聚合表单
  aggregationSubDataUnits?: DataUnitBaseVO[]; // 聚合-子数据单元
  onlyConditionGroups?: boolean; // 是否只显示条件组
  isMainDataUnit?: boolean; // 是否是主数据单元
  dataUnitName?: string; // 数据单元名称
  dataUnitId?: number; // 数据单元id
  tags: any[];
  canRelateTags: any[];
  calcFields?: any[];
  conditionGroups?: any;
  extConfig?: any;
  additionalConditionGroups?: any[];
  onOk?: (values) => void;
  onCancel?: () => void;
}

const DataQuerySetting = (props: Iprops) => {
  const {
    disabled = false,
    fieldType,
    groupFlag = false,
    aggregationSubDataUnits = [],
    onlyConditionGroups = false,
    isMainDataUnit,
    dataUnitName,
    dataUnitId,
    conditionGroups,
    additionalConditionGroups,
    extConfig,
    tags = [],
    canRelateTags,
    calcFields,
    onOk,
    onCancel,
  } = props;
  const [form] = Form.useForm();
  const groupFilterNewConfigWatcher = Form.useWatch('groupFilterNewConfig', form);
  const sameGroupQueryType = Form.useWatch('sameGroupQueryType', form);
  const dataQueryType = Form.useWatch('dataQueryType', form);

  const sameGroupQueryOptions = useMemo(() => {
    return [
      { label: '仅子数据', value: SAME_GROUP_QUERY_TYPE.ONLY_SUB },
      { label: '全部数据', value: SAME_GROUP_QUERY_TYPE.ALL, disabled: dataQueryType === DATA_QUERY_TYPE.SAME || disabled },
    ];
  }, [dataQueryType, disabled]);

  useEffect(() => {
    console.log('groupFilterNewConfigWatcher', groupFilterNewConfigWatcher);
  }, [groupFilterNewConfigWatcher]);

  const handleGroupFilterNewConfigSwitch = (checked: boolean) => {
    if (!checked) {
      form.setFieldsValue({
        groupFilterNewConfig: null,
      });
      return;
    }

    form.setFieldsValue({
      groupFilterNewConfig: {
        groupTags: [],
        timeTag: [],
      },
    });
  };

  useEffect(() => {
    const defaultExtConfig = getExtConfig(extConfig);
    form.setFieldsValue(defaultExtConfig);
  }, [extConfig, form]);

  return (
    <ConfigProvider theme={{ token: { fontSize: 14 } }}>
      <Modal
        title="数据查询规则"
        open
        onCancel={onCancel}
        onOk={async () => {
          const values = await form.validateFields();
          const { sameGroupQueryType, dataQueryType, conditionGroups, sortTags, groupFilterNewConfig, subDataUnitIds, addForm } = values;
          let allConditionGroups = [...conditionGroups];
          if (addForm?.additionalConditionGroups?.conditionGroups?.length > 0) {
            allConditionGroups = [...allConditionGroups, ...(addForm.additionalConditionGroups.conditionGroups || [])];
          }
          // console.log('allConditionGroups-----------',values, allConditionGroups);
          onOk &&
            onOk({
              extConfig: {
                sameGroupQueryType,
                dataQueryType,
                sortTags,
                groupFilterNewConfig,
                subDataUnitIds,
              },
              conditionGroups,
              allConditionGroups,
              additionalConditionGroups: addForm?.additionalConditionGroups?.conditionGroups || [],
            });
        }}
        size="large"
      >
        <Form form={form} className={styles.dataQuerySetting}>
          {groupFlag && (
            <>
              <Form.Item className={styles.ruleItem} label={<span className={styles.ruleLabel}>数据单元类型</span>}>
                <span>{dataUnitName}</span>
              </Form.Item>
              <Form.Item className={styles.ruleItem} label={'选择数据单元'} name="subDataUnitIds">
                <Select
                  style={{ width: 520 }}
                  disabled={disabled}
                  mode="multiple"
                  options={aggregationSubDataUnits.map((item) => ({ label: item.name, value: item.dataUnitId }))}
                />
              </Form.Item>
            </>
          )}

          {!onlyConditionGroups && !groupFlag && (
            <>
              <Form.Item
                className={styles.ruleItem}
                label={<span className={styles.ruleLabel}>{typeof isMainDataUnit === 'boolean' ? (isMainDataUnit ? '主' : '副') : ''}数据单元</span>}
              >
                <span>{dataUnitName || '-'}</span>
              </Form.Item>
              <Form.Item
                className={styles.ruleItem}
                label={
                  <span className={styles.ruleLabel}>
                    同组数据查询&nbsp;
                    <Tooltip title="可选择同组数据显示的数据内容。对数据进行拆分或使用组合标签录入数据，都将产生同组数据。同组数据包括一条主数据与多条子数据。仅子数据（默认）：数据查询时，仅能查询到子数据。全部数据：数据查询时，能同时查询到主、子数据。">
                      <QuestionCircleOutlined />
                    </Tooltip>
                  </span>
                }
                name="sameGroupQueryType"
              >
                <Radio.Group disabled={disabled} options={sameGroupQueryOptions} />
              </Form.Item>
              <Form.Item
                className={styles.ruleItem}
                label={
                  <span className={styles.ruleLabel}>
                    数据查询方式&nbsp;
                    <Tooltip
                      title={
                        <div>
                          基于单条数据：在查询数据时，单条数据需要满足所有条件，即可被查询出来。
                          <br />
                          基于同条数据：在查询数据时，同条数据中若有某条数据满足条件组，就算满足条件，即可被查询出来。
                        </div>
                      }
                    >
                      <QuestionCircleOutlined />
                    </Tooltip>
                  </span>
                }
                name="dataQueryType"
              >
                <Radio.Group disabled={disabled}>
                  <Radio value={DATA_QUERY_TYPE.SINGLE}>
                    基于单条数据&nbsp;
                    <Tooltip title="单条数据概念：基于一个发起数据，产生的后续所有数据，有同一个原始数据id。">
                      <QuestionCircleOutlined />
                    </Tooltip>
                  </Radio>
                  <Radio value={DATA_QUERY_TYPE.SAME} disabled={sameGroupQueryType === SAME_GROUP_QUERY_TYPE.ALL || disabled}>
                    基于同条数据&nbsp;
                    <Tooltip title="同条数据概念：对同一条发起数据，进行操作后产生的后续所有数据。">
                      <QuestionCircleOutlined />
                    </Tooltip>
                  </Radio>
                </Radio.Group>
              </Form.Item>
            </>
          )}

          {/* 数据查询条件组 */}
          <div className={styles.ruleTitle} style={{ paddingTop: 8, marginBottom: 12 }}>
            数据查询条件组
          </div>
          <FilterGroup disabled={disabled} conditionGroups={conditionGroups} tags={tags} canRelateTags={canRelateTags} calcFields={calcFields} />

          {!onlyConditionGroups && (
            <>
              {
                // 标签独立查询 字段里才会有这个配置
                fieldType && (
                  <div className={styles.ruleTitle} style={{ paddingTop: 16 }}>
                    <div className={styles.ruleTitleText}>
                      标签独立查询&nbsp;
                      <Tooltip title="开启后，将该标签的来源于数据独立查询的结果，不依赖表单的数据查询结果。">
                        <QuestionCircleOutlined /> :
                      </Tooltip>
                    </div>
                    <div className={styles.ruleTitleOpt}>
                      <Form.Item name="ifDependentQuery" initialValue={true} noStyle>
                        <Switch disabled={fieldType === FieldType.IndependentField || disabled} />
                      </Form.Item>
                    </div>
                  </div>
                )
              }
              {isMainDataUnit && (
                <>
                  {/* 结果排序 */}
                  <div className={styles.ruleTitle} style={{ paddingTop: 16, marginBottom: 12 }}>
                    <div className={styles.ruleTitleText}>
                      结果排序&nbsp;
                      <Tooltip
                        title={
                          <div>
                            优先级以先后顺序为准，第一条优先级最高，第二条次之，以此类推，排序规则必须在前置的排序规则上生效。
                            <br />
                            场景示例：第一条优先级规则相同的数据，将按第二条排序规则进行排序。
                          </div>
                        }
                      >
                        <QuestionCircleOutlined /> :
                      </Tooltip>
                    </div>
                  </div>
                  <SortTags dataUnitId={dataUnitId} tags={tags} disabled={disabled} />

                  {/* 分组筛选最新 */}
                  <div className={styles.ruleTitle} style={{ paddingTop: 16, marginBottom: 12 }}>
                    <div className={styles.ruleTitleText}>
                      分组筛选最新&nbsp;
                      <Tooltip title="根据分组标签的值对数据进行分组，按时间标签取最新一条数据">
                        <QuestionCircleOutlined /> :
                      </Tooltip>
                    </div>
                    <div className={styles.ruleTitleOpt}>
                      <Switch
                        checked={!!groupFilterNewConfigWatcher}
                        disabled={disabled}
                        onChange={(checked) => {
                          handleGroupFilterNewConfigSwitch(checked);
                        }}
                      />
                    </div>
                  </div>
                  <Form.Item name="groupFilterNewConfig" noStyle>
                    {!!groupFilterNewConfigWatcher && <GroupFilter dataUnitId={dataUnitId} tags={tags} disabled={disabled} />}
                  </Form.Item>
                </>
              )}
              {/* 新增条件组 */}
              {additionalConditionGroups && (
                <>
                  <div className={styles.ruleTitle} style={{ paddingTop: 8, marginBottom: 12 }}>
                    <div className={styles.ruleTitleText}>新增条件组</div>
                  </div>
                  <Form.Item name="addForm" noStyle>
                    <AddQuery
                      additionalConditionGroups={additionalConditionGroups}
                      tags={tags}
                      canRelateTags={canRelateTags}
                      calcFields={calcFields}
                      conditionGroups={conditionGroups}
                    />
                  </Form.Item>
                </>
              )}
            </>
          )}
        </Form>
      </Modal>
    </ConfigProvider>
  );
};

export default DataQuerySetting;
