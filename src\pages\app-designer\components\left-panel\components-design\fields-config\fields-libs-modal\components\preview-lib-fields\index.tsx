import { CalcFieldVO, CalcType } from '@/types/calc-field';
import { Modal } from '@gwy/components-web';
import { calcTypeMap } from '../..';
import CalcCalcDesc from '../calc-calc/desc';
import FmlDesc from '../fml-calc/components/fml-desc';
import styles from './index.less';

interface Props {
  onOk: () => void;
  onCancel: () => void;
  fieldItem: CalcFieldVO;
}
const PreviewLibFields = ({ fieldItem, onOk, onCancel }: Props) => {
  const { createPostName, createUserName, fieldName, calcType } = fieldItem;

  return (
    <>
      <Modal
        open
        styles={{
          body: {
            padding: 0,
          },
        }}
        title="计算字段"
        onOk={onOk}
        onCancel={onCancel}
      >
        <div className={styles.container}>
          <div className={styles.body}>
            <div className={styles.content}>
              <div className={styles.leftContent}>创建岗位：</div>
              <div className={styles.rightContent}>{`${createPostName}-${createUserName}`}</div>
            </div>
            <div className={styles.content}>
              <div className={styles.leftContent}>字段名称：</div>
              <div className={styles.rightContent}>{fieldName}</div>
            </div>
            <div className={styles.content}>
              <div className={styles.leftContent}>计算方式：</div>
              <div className={styles.rightContent}>{calcType ? calcTypeMap[calcType] : ''}</div>
            </div>

            <div className={styles.content} style={fieldItem.calcType === CalcType.Calculation ? { flexDirection: 'column', gap: 10 } : {}}>
              <div className={styles.leftContent}>{fieldItem.calcType === CalcType.Calculation ? '计算规则：' : `字段公式：`}</div>
              <div className={styles.rightContent}>
                {fieldItem.calcType === CalcType.FormulasCalc && <FmlDesc calcField={fieldItem} />}
                {fieldItem.calcType === CalcType.Calculation && <CalcCalcDesc calcField={fieldItem} />}
              </div>
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
};
export default PreviewLibFields;
