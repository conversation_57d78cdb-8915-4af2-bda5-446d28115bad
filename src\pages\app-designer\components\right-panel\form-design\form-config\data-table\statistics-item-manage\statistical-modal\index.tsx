import { AppDesignerContext } from '@/pages/app-designer/context';
import { SourceType } from '@/types/form-field';
import { FormTableConfig } from '@/types/form-table';
import { StatisticalItem } from '@/types/statistic-item';
import { genUuid } from '@/utils';
import { getFormFieldDisplayName, getMetaTagUniqueId } from '@/utils/metadata';
import { PlusCircleOutlined } from '@ant-design/icons';
import { Form, Input, Modal } from '@gwy/components-web';
import { Fragment, useContext, useEffect } from 'react';
import { CalcSymbol } from '../const';
import StatCalcItem from '../statistical-calc';
import styles from './index.less';
import LocationSelect from './location-select';

type Props = {
  table?: FormTableConfig;
  record?: StatisticalItem;
  isViewMode?: boolean;
  onCancel?: () => void;
  onOk?: (values) => void;
};

const StatisticalModal = ({ table, record, isViewMode, onCancel, onOk }: Props) => {
  const { tagsMap } = useContext(AppDesignerContext);
  const [form] = Form.useForm();
  const { validateFields, setFieldsValue, setFieldValue } = form;

  useEffect(() => {
    if (record) {
      setFieldsValue(record);
    }
  }, [record]);

  const handleCancel = () => {
    onCancel && onCancel();
  };

  const handleOk = async () => {
    const values = await validateFields();
    onOk && onOk({ ...values });
  };

  return (
    <Modal
      open
      title={record ? '编辑统计项' : '添加统计项'}
      onCancel={handleCancel}
      onOk={handleOk}
      centered
      width={500}
      styles={{
        body: {
          height: 500 - 80,
        },
      }}
    >
      <div className={styles.container}>
        <div className={styles.body}>
          <Form form={form} disabled={isViewMode}>
            <Form.Item hidden name={['statisticId']} initialValue={genUuid()} />
            <Form.Item label="名称" name={['statisticName']} rules={[{ required: true, message: '请输入' }]}>
              <Input placeholder="请输入" />
            </Form.Item>

            {/* 统计计算 */}
            <Form.Item label="公式" style={{ marginBottom: 0 }} required>
              <Form.List name={['formulas']} initialValue={[{}]}>
                {(fields, { add, remove }) => {
                  return fields.map((field, index) => {
                    return (
                      <Fragment key={field.key}>
                        <StatCalcItem
                          remove={remove}
                          prefixName={['formulas']}
                          field={field}
                          index={index}
                          formFields={table?.fields}
                          isViewMode={isViewMode}
                        />
                        {index + 1 === fields.length && !isViewMode && (
                          <a onClick={() => add({ symbol: CalcSymbol.ADD })}>
                            <PlusCircleOutlined style={{ marginRight: 6 }} />
                            添加公式
                          </a>
                        )}
                      </Fragment>
                    );
                  });
                }}
              </Form.List>
            </Form.Item>

            <Form.Item style={{ marginTop: 15 }} label="位置" required>
              <>
                <Form.Item name={['location']} noStyle rules={[{ required: true, message: '请输入' }]}>
                  <LocationSelect
                    options={table?.fields?.map((f) => {
                      return {
                        label: getFormFieldDisplayName(f, tagsMap),
                        value: getMetaTagUniqueId(f),
                        disabled: table.statisticList?.some(
                          (s) =>
                            (s.location.sourceType === SourceType.Tag &&
                              s.location?.dataUnitId === f.dataUnitId &&
                              s.location?.sourceId === f.sourceId) ||
                            (s.location.sourceType === SourceType.Field && s.location?.sourceId === f.fieldConfig?.fieldId),
                        ),
                      };
                    })}
                    formFields={table?.fields}
                  />
                </Form.Item>
                <span style={{ marginLeft: 10 }}>下方</span>
              </>
            </Form.Item>
          </Form>
        </div>
      </div>
    </Modal>
  );
};

export default StatisticalModal;
