import CommonMetaDynamicItem from '@/components/common-piece-dynamic-item';
import EditorPreview from '@/components/editor-preview';
import MetaDynamicItem from '@/components/meta-dynamic-item';
import { UseIn } from '@/components/meta-dynamic-item/const';
import DataTable from '@/components/meta-dynamic-item/data-table';
import { handleTableData } from '@/components/meta-dynamic-item/data-table/utils';
import Editor, { IEditorRef, MODE_TYPE } from '@/components/onlyoffice-editor';
import { ButtonOperateType, ButtonStyle } from '@/const/button-config';
import { useBridge } from '@/hooks';
import { AppType } from '@/pages/app-designer/const';
import { appAPI, datasourceDataAPI, orgDataUnitAPI, pendingAPI, systemDatametaAPI } from '@/services';
import { getSubDataUnitConfigByVersionId, getSubDataUnitData } from '@/services/app-sub';
import { queryMutData } from '@/services/datasource-data';
import { FormDataOperateType, FormVO } from '@/types/app';
import { FormDataDraftDetailVO, FormDataDraftItemVO } from '@/types/app-data';
import { SourceType } from '@/types/form-field';
import { Post } from '@/types/post';
import { DataUnitTagVO } from '@/types/tag';
import { genUuid } from '@/utils';
import eventEmitter, { EventName } from '@/utils/event-emitter';
import { getMetaTagUniqueId } from '@/utils/metadata';
import { Button, Col, Empty, Form, LayoutPage, message, Modal, Row } from '@gwy/components-web';
import classNames from 'classnames';
import { cloneDeep, groupBy, isEmpty, map, uniq } from 'lodash-es';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import HandleDataHistory from '../handle-data-history';
import MainDataView from '../main-data-view';
import SplitDataModal from '../split-data-modal';
import styles from './index.less';

type Props = {
  isPreview?: boolean;
  dataId?: string; // 由列表进入，查看数据
  formId?: number;
  formVersionId?: number;
  post?: Post;
  isReturn?: boolean; // 退回
  bizId?: string;
  bizType?: number;
  isSameGroup?: boolean; // 是否是有列表同组数据
  isSamePiece?: boolean; // 是否是由列表同条数据
  conditionMap?: any; // 筛选条件的动态入参map
  allDataIds?: any[]; // dataId列表，主数据和子数据id
  filters?: any; // 同条数据筛选条件
  dataGroupOption?: any; // 聚合查询条件
  [key: string]: any;
};

enum BizModalEnums {
  CLOSE = 'CLOSE',
  HISTORYHANDLE = 'HISTORYHANDLE', // 处理历史
  SPLITDATA = 'SPLITDATA', // 数据拆分
  TIMEQUERY = 'TIMEQUERY', // 实时查询
  MAINDATAVIEW = 'MAINDATAVIEW', // 查看主数据
}

export enum SPILTETYPEENUM {
  COMBINE_TAG = 'COMBINE_TAG', // 组合标签录入
  DATA_SPLIT = 'DATA_SPLIT', // 数据拆分
}

export enum DATATYP_SPLTE_EENUM {
  MAIN = 'MAIN',
  SUB = 'SUB',
}

const AppForm = (props: Props) => {
  const {
    isPreview,
    dataId,
    formId: defaultFormId,
    formVersionId: defaultFormVersionId,
    post,
    isReturn,
    bizId,
    bizType,
    isSameGroup,
    isSamePiece,
    conditionMap,
    allDataIds,
    filters,
    dataGroupOption,
  } = props;
  const [formId, setFormId] = useState<number>(defaultFormId);
  const [formVersionId, setFormVersionId] = useState<number>(defaultFormVersionId);
  console.log(allDataIds, props, 'allDataIds-------------');
  // 制表的配置信息
  const [formVersion, setFormVersion] = useState<FormVO>({});

  const [cTagsMap, setCTagsMap] = useState<Record<number, DataUnitTagVO>>({});
  // 表单数据
  const [records, setRecords] = useState<FormDataDraftItemVO[]>();
  // 审批退回的数据
  const [draftDetail, setDraftDetail] = useState<FormDataDraftDetailVO>({});
  const [loading, setLoading] = useState(false);
  // 动态表格信息
  const [tableList, setTbaleList] = useState([]);
  // 初始值信息
  const [initDataInfo, setInitDataInfo] = useState({});
  // 组合标签的数据
  const [combineRecords, setCombineRecords] = useState([]);

  /**
   * 表格加载中
   * 可能会多个表格并行加载数据
   * 采用计数方式统计loading状态
   */
  const [tablesLoading, setTablesLoading] = useState(0);
  const handleTablesLoading = useCallback(
    (loading) => {
      setTablesLoading((pre) => (loading ? pre + 1 : pre - 1));
    },
    [setTablesLoading],
  );
  // 数据表格ref
  const tableRefs = useRef({});
  // 组合标签ref
  const combineTagRef = useRef(null);

  const [form] = Form.useForm();

  console.log(cTagsMap, '===cTagsMap');
  // 编辑器ref
  const editorRef = useRef<IEditorRef>();

  // 预览弹框
  const [previewModal, setPreviewModal] = useState<{
    open?: boolean;
    url?: string;
  }>({});
  // 打开不同的业务弹窗
  const [openBizModal, setOpenBizModal] = useState<{
    open?: boolean;
    type?: string;
    data?: any;
  }>({
    open: false,
    type: BizModalEnums.CLOSE,
    data: null,
  });
  const [leftWidth, setLeftWidth] = useState(528);
  // const [isDragging, setIsDragging] = useState(false);
  const isDargRef = useRef(null);
  const [rightMargin, setRightMargin] = useState(0);
  const containerRef = useRef(null);
  const dragHandleRef = useRef(null);

  // 处理鼠标按下事件
  const handleMouseDown = (e) => {
    e.preventDefault();
    e.stopPropagation();
    // setIsDragging(true);
    isDargRef.current = true;
    // console.log(isDragging, 'isDragging----------')
    // 添加事件监听
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  // 处理鼠标移动事件
  const handleMouseMove = (e) => {
    const containerRect = containerRef.current.getBoundingClientRect();
    const leftWidth = Math.abs(Math.abs(e.clientX) - containerRect.left);
    const newWidth = containerRect.width - leftWidth;
    // console.log(newWidth, containerRect, newWidth, e.clientX,isDragging, 'newWidth----------');
    if (isDargRef.current) {
      // 限制最小和最大宽度
      if (newWidth >= 200 && newWidth <= 800) {
        setLeftWidth(newWidth);
        // calculateRightMargin();
      }
    }
  };

  // 处理鼠标释放事件
  const handleMouseUp = () => {
    // setIsDragging(false);
    isDargRef.current = false;
    console.log('handleMouseUp-------', isDargRef.current);
    // 移除事件监听
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  };

  const bridge = useBridge();

  const unitToDataIdMap = useMemo(() => {
    const record = records?.[0] || {};
    const map = {};
    formVersion?.dataUnits?.forEach((dataUnit) => {
      let codeKey = '_id';
      if (!dataUnit?.mainDataUnit) {
        codeKey = `${dataUnit.dataUnitId}_id`;
      }
      map[dataUnit.dataUnitId] = record?.[codeKey];
    });
    // console.log(map, 'map----------')
    return map;
  }, [records, formVersion]);

  // 获取完整标签信息
  const fetchTags = async (dataUnitIds) => {
    const dataUnits = await orgDataUnitAPI.getOrgDataUnitsTags({ orgId: post?.orgId, dataUnitIds });
    setCTagsMap(
      dataUnits?.reduce((acc, cur) => {
        cur.tagList?.forEach((tag) => {
          acc[getMetaTagUniqueId(tag)] = tag;
        });
        return acc;
      }, {} as Record<number, DataUnitTagVO>),
    );
  };

  const [cFields, allOutPut] = useMemo(() => {
    console.log(formVersion, dataGroupOption, 'formVersion-----');
    // if (!isEmpty(dataGroupOption)) {
    //   const cFields = formVersion?.extConfig?.fields || [];
    //   const isAllOutput = cFields.every((field) => field.readonly);
    // }
    const cFields = formVersion?.extConfig?.fields || [];
    const isAllOutput = cFields.every((field) => field.readonly);
    return [cFields, isAllOutput];
  }, [formVersion, dataGroupOption]);
  // 获取初始默认值信息
  const getInitDefaultData = async (formVersionId) => {
    const data = await datasourceDataAPI.getDefaultValue({
      formVersionId,
      postId: post?.postId,
    });
    setInitDataInfo(data);
  };
  const setTableRef = useCallback(
    (id) => (node) => {
      if (node) {
        tableRefs.current[id] = node;
      }
    },
    [],
  );

  useEffect(() => {
    if (!isEmpty(dataGroupOption)) {
      const { formVersionId, dataUnitId } = dataGroupOption;
      findGroupDataSingle(formVersionId, dataUnitId);
      return;
    }
    (async () => {
      if (formVersionId) {
        // 获取制表配置信息
        const formVersion: FormVO = await appAPI.getFormVersion(formVersionId);
        setFormVersion(formVersion);
        // 获取所有用到的数据单元id
        const commonGroupIds = formVersion.dataUnits.map((item) => item.dataUnitId) || [];
        const combineTagIds = formVersion?.extConfig?.combineTagConfig?.fields?.map((field) => field?.dataUnitId) || [];
        const tableUnitIds =
          (formVersion?.extConfig?.tableConfigs || [])?.reduce((acc, cur) => {
            const ids = cur.dataUnits?.map((d) => d.dataUnitId) || [];
            acc.push(...ids);
            return acc;
          }, [] as number[]) || [];
        const allDataUnitIds = uniq([...commonGroupIds, ...combineTagIds, ...tableUnitIds]);
        fetchTags(allDataUnitIds);
        setTbaleList(formVersion?.extConfig?.tableConfigs || []);
        // if (formVersion?.extConfig?.tableConfigs?.length > 0) {
        //   (formVersion?.extConfig?.tableConfigs || []).forEach((table) => {
        //     if (!tableRefs?.current[table.id]) {
        //       tableRefs.current[table.id] = useRef(null);
        //     }
        //   });
        // }
        editorRef.current?.setFileUrl({ fileUrl: formVersion.wordFileUrl, mode: formVersion.wordReadonly ? MODE_TYPE.VIEW : MODE_TYPE.EDIT });
      }
    })();
  }, [formVersionId, dataGroupOption]);

  // 初次发起数据需要获取默认值

  useEffect(() => {
    if (formVersion && !dataId && !isReturn) {
      getInitDefaultData(formVersionId);
    }
  }, [formVersionId, dataId, isReturn]);

  // 聚合查询子应用配置
  const findGroupDataSingle = async (formVersionId, dataUnitId) => {
    // const formVersion = awiat
    const data = await getSubDataUnitConfigByVersionId({
      dataUnitId,
      formVersionId,
    });
    setFormVersion({
      ...(data || {}),
      dataUnits: [
        {
          dataUnitId: data?.dataUnitId,
          name: data?.dataUnitName,
          mainDataUnit: true,
        },
      ],
    });
    fetchTags([data?.dataUnitId]);
    setTbaleList(formVersion?.extConfig?.tableConfigs || []);
    editorRef.current?.setFileUrl({ fileUrl: data.wordFileUrl, mode: formVersion.wordReadonly ? MODE_TYPE.VIEW : MODE_TYPE.EDIT });
  };

  // 聚合查询
  const findsingleUnitDataGroyup = async (dataId, formVersionId, dataUnitId) => {
    const data = await getSubDataUnitData(dataId, {
      formVersionId,
      dataUnitId,
    });
    const { items } = data || {};
    // const records = (items || [])?.map(item => item?.record)
    // console.log(records, 'records--------')
    setRecords(items);
  };

  useEffect(() => {
    // 有列表进入，聚合查询
    if (!isEmpty(dataGroupOption)) {
      const { formVersionId, dataUnitId, dataId } = dataGroupOption;
      findsingleUnitDataGroyup(dataId, formVersionId, dataUnitId);
      return;
    }
    // 由列表进入，获取数据
    if (dataId && !isEmpty(formVersion) && !isSamePiece) {
      fetchFormDataDetail();
    }
    // 同条数据需要将同一标签的值形成ops
    if (allDataIds?.length > 0 && !isEmpty(formVersion) && isSamePiece) {
      let allDataUnits = formVersion?.dataUnits;
      // console.log(filters, 'filters------');
      allDataUnits = allDataUnits?.map((item) => {
        if (item.dataUnitId === filters?.dataUnitId) {
          return {
            ...item,
            ...filters,
          };
        }
        return item;
      });
      fetchFormDataDetail(allDataIds?.join(','), allDataUnits);
    }
  }, [dataId, formVersion, allDataIds, filters, isSamePiece, dataGroupOption]);

  const handleMutiData = (values) => {
    const mainDataUnit = formVersion?.dataUnits?.find((item) => item?.mainDataUnit);
    let results = {};
    const waitValues = [values?.[0], ...(values?.[0]?.children || [])];
    // console.log(waitValues, 'waitValues-------------');
    cFields?.forEach((tag) => {
      let tagOps = [];
      const isMain = tag?.dataUnitId === mainDataUnit?.dataUnitId;
      const codeKey = isMain ? tag?.code : `${tag?.dataUnitId}_${tag?.code}`;
      waitValues?.forEach((rowValue) => {
        // console.log(rowValue, isMain, codeKey,tag, mainDataUnit,'rowValue----------')
        const value = rowValue?.[codeKey]?.value;
        const hasValue = tagOps?.filter((ops) => ops?.value === value)?.length > 0;
        if (!hasValue && rowValue?.[codeKey]) {
          tagOps.push({
            ...(rowValue[codeKey] || {}),
            value: rowValue[codeKey]?.value,
            data: rowValue[codeKey]?.data,
          });
        }
      });
      results[getMetaTagUniqueId(tag)] = {
        value: waitValues[0]?.[codeKey]?.data,
        data: waitValues[0]?.[codeKey]?.data,
        tagOps: cloneDeep(tagOps),
      };
    });
    return [results];
  };

  // 由列表进入，获取数据
  const fetchFormDataDetail = async (paramsDataIds?: any, allDataUnits?: any) => {
    const DataUnitMain = formVersion?.dataUnits?.find((dataUnit) => dataUnit?.mainDataUnit) || {};
    const params = {
      formVersionId,
      postId: post?.postId,
      // pageSize: pagination.pageSize,
      // currentPage: pagination.current,
      queryConfig: {
        dataUnits: allDataUnits ? allDataUnits : formVersion?.dataUnits,
        queryFormType: 1, // 1=基础分组，2=数据表格
        dataId: paramsDataIds ? paramsDataIds : dataId,
        tableConFig: {
          dataUnitId: DataUnitMain?.dataUnitId,
          dataId,
        },
      },
    };
    let data: any = await queryMutData(params);
    if (paramsDataIds) {
      data = handleMutiData(data?.records);
      setRecords(data);
    } else {
      setRecords(data?.records);
    }
    // console.log(data, 'data----------');
  };

  // 审批退回-------------start
  useEffect(() => {
    if (isReturn) {
      fetchDraftDetail();
    }
  }, [isReturn]);

  // 审批退回数据
  const fetchDraftDetail = async () => {
    const data = await systemDatametaAPI.getOrgDataUnitApproveData(bizId);
    setDraftDetail(data);
    setRecords(data?.draftItems);

    setFormId(data.formId);
    setFormVersionId(data.formVersionId);
    const combineRecords = data?.draftItems?.map((item) => item?.record)?.filter((record) => record.data_type === 2);
    // console.log(combineRecords, 'combineRecords-----------')
    setCombineRecords(combineRecords);
  };
  // 审批退回-------------end

  // 数据转换回显
  const transformData = (records, tags, dataUnits) => {
    const mainDataUnit = dataUnits?.find((item) => item?.mainDataUnit);
    console.log(mainDataUnit, 'mainDataUnit');
    const handleRecords = records.map((record) => {
      const values = {
        ...(record || {}),
      };
      tags?.forEach((field) => {
        if (field.sourceType === SourceType.Field) {
          return;
        }
        // const record = records.find((item) => item.dataUnitId === field.dataUnitId)?.record;
        const isMain = field.dataUnitId === mainDataUnit?.dataUnitId;
        const codeKey = isMain ? field.code : `${field.dataUnitId}_${field.code}`;
        values[getMetaTagUniqueId(field)] = {
          value: field.readonly ? record[codeKey] : record[codeKey]?.data,
          data: record[codeKey]?.data,
          $$id: genUuid(),
        };
      });
      return {
        ...values,
      };
    });
    return handleRecords;
  };
  // 设置表单值
  useEffect(() => {
    if (records?.length > 0 && cFields?.length > 0 && Object.keys(cTagsMap).length > 0) {
      const values = {};
      if (!records[0]?.dataUnitId && !isSamePiece) {
        const values = transformData(records, cFields, formVersion?.dataUnits);
        console.log(values, 'values------------');
        form.setFieldsValue(values[0]);
      } else if (!isSamePiece) {
        cFields?.forEach((field) => {
          if (field.sourceType === SourceType.Field) {
            return;
          }
          const record = records.find((item) => item.dataUnitId === field.dataUnitId)?.record;
          values[getMetaTagUniqueId(field)] = {
            value: field.readonly ? record[field.code] : record[field.code]?.data,
          };
        });
        form.setFieldsValue(values);
      }
      if (isSamePiece) {
        // console.log(records, 'records--------');
        form.setFieldsValue(records[0]);
      }
    }
    if (combineRecords?.length > 0) {
      const tags = (formVersion?.extConfig?.combineTagConfig?.fields || [])?.map((field) => {
        const tag = cTagsMap[getMetaTagUniqueId(field)];
        return {
          ...field,
          ...tag,
        };
      });
      const combineValues = transformData(combineRecords, tags, formVersion?.dataUnits);
      // console.log(combineValues, 'combineValues');
      combineTagRef?.current?.setValues(combineValues);
    }
  }, [records, cFields, cTagsMap, combineRecords, dataGroupOption]);

  const getCombineValues = useCallback(async () => {
    let records = [];
    if (bizId && isReturn) {
      const data = await systemDatametaAPI.getOrgDataUnitApproveData(bizId);
      records = data?.draftItems?.map((item) => item?.record)?.filter((record) => record.data_type === 2);
      setCombineRecords(records);
    }
    if (dataId) {
      const data = await appAPI.getFormDataDetail(dataId, { formVersionId });
      records = data?.draftItems?.map((item) => item?.record)?.filter((record) => record.data_type === 2);
      setCombineRecords(records);
    }
    return records;
  }, [isReturn, dataId, bizId, formVersionId]);

  useEffect(() => {
    if (initDataInfo && cFields?.length > 0) {
      console.log(initDataInfo, cFields, 'initDataInfo--------');
      // handleTableData()
      const values = {};
      cFields?.forEach((field) => {
        if (field.sourceType === SourceType.Field) {
          return;
        }
        // const record = records.find((item) => item.dataUnitId === field.dataUnitId)?.record;
        values[getMetaTagUniqueId(field)] = {
          value: field.readonly ? initDataInfo[getMetaTagUniqueId(field)] : initDataInfo[getMetaTagUniqueId(field)]?.data,
        };
      });
      console.log(values, 'values----');
      form.setFieldsValue(values);
      // form.setFieldsValue(initDataInfo);
    }
  }, [initDataInfo, cFields]);

  useEffect(() => {
    const listener = (params) => {
      const { dataUnitId, tagId, value } = params || {};
      // TODO 动态入参判断条件
      // 如果有动态入参标签，并且发起的事件中时改标签值改变，需要重新获取一遍数据
      // getTableData()
    };

    eventEmitter.on(EventName.dynamicParamChange, listener);

    return () => {
      eventEmitter.off(EventName.dynamicParamChange, listener);
    };
  }, []);

  type FormValuesType = Record<
    string,
    {
      tagId: number;
      tagName: string;
      code: string;
      dataUnitId: number;
      value: any;
    }
  >;
  const genRowDatas = (values: FormValuesType, formDataOperateType?) => {
    const groupId = genUuid();
    return map(groupBy(Object.values(values), 'dataUnitId'), (dataUnitTags, dataUnitId) => {
      console.log(cFields, dataUnitTags, values, 'tag------');
      return {
        dataUnitId,
        preDataId: unitToDataIdMap[dataUnitId],
        groupId,
        formDataOperateType: formDataOperateType ?? FormDataOperateType.Launch,
        tagDatas: dataUnitTags
          ?.filter((tag) => !cFields?.find((f) => getMetaTagUniqueId(f) === getMetaTagUniqueId(tag))?.readonly)
          .map((tag) => ({
            tagId: tag.tagId,
            tagName: tag.tagName,
            code: tag.code,
            value: tag.value,
          })),
      };
    });
  };

  const handleRowData = (tags, values, formDataOperateType, tableId = undefined, mainData = []) => {
    const groupMap = {};
    tags?.reduce((prev, cur) => {
      if (!groupMap[cur?.dataUnitId]) {
        return (groupMap[cur?.dataUnitId] = [cur]);
      } else {
        return groupMap[cur?.dataUnitId].push(cur);
      }
    }, {});
    console.log(groupMap, 'groupMap----------------');
    // 对行数据数组进行处理，将dataUnitId-tagId为键名的数据形成一个对象数组
    let res = [];
    values?.forEach((valueItem, index) => {
      const groupId = valueItem?.groupId ? valueItem?.groupId : genUuid();
      Object.keys(groupMap)?.forEach((dataUnitId) => {
        let buildRow = {};
        const spiltMainData = mainData?.find((singalData) => singalData?.dataUnitId === dataUnitId);
        groupMap[dataUnitId]
          ?.filter((tag) => !tag?.readonly)
          ?.forEach((tag) => {
            const key = getMetaTagUniqueId(tag, dataUnitId as any);
            tag['value'] = valueItem[key]?.data?.data ? valueItem[key]?.data : valueItem[key];
            tag['tagId'] = tag?.tagId || tag?.sourceId;
          });
        const buildTagDatas = cloneDeep(groupMap[dataUnitId]);
        if (!isEmpty(spiltMainData)) {
          buildTagDatas.push(...spiltMainData?.tagDatas);
        }
        buildRow = {
          tagDatas: buildTagDatas,
          dataUnitId,
          preDataId: tableId ? dataId : unitToDataIdMap[dataUnitId],
          formDataOperateType: formDataOperateType ?? FormDataOperateType.Launch,
          splitType: tableId ? undefined : SPILTETYPEENUM.COMBINE_TAG, // 1 为组合录入，2为数据拆分
          tableId,
          dataType: tableId ? undefined : DATATYP_SPLTE_EENUM.SUB, // 1 为主数据，子为主数据
          groupId,
        };
        // valueItem['tagDatas'] = groupMap[dataUnitId]
        res.push(buildRow);
      });
    });
    if (mainData?.length > 0) {
      const mainDatas = mainData?.map((singalData) => ({
        ...singalData,
        dataType: DATATYP_SPLTE_EENUM.MAIN,
        splitType: SPILTETYPEENUM.COMBINE_TAG,
        formDataOperateType: formDataOperateType ?? FormDataOperateType.Launch,
        tableId,
      }));
      res.push(...mainDatas);
      console.log(res, 'res-------');
    }

    return res;
  };

  const handleCombineRowDatas = (values, formDataOperateType, mainData = []) => {
    console.log(mainData, 'values-------------');

    // formVersion?.extConfig?.combineTagConfig?.fields

    if (values?.length > 0) {
      return handleRowData(formVersion?.extConfig?.combineTagConfig?.fields, values, formDataOperateType, '', mainData);
    }
    return [];
  };

  const handleSingalTableRowData = (tableConfig, values, formDataOperateType) => {
    // const tags = tableRefs?.current?
    const { id, fields } = tableConfig;
    console.log(fields, values, 'fields---------------');
    return handleRowData(fields, values, formDataOperateType, id);
  };

  const handleAllTables = async (formDataOperateType) => {
    let results = [];
    const { tableConfigs } = formVersion?.extConfig;
    if (tableConfigs && Array.isArray(tableConfigs)) {
      for (const singleTable of tableConfigs) {
        if (singleTable?.id && tableRefs?.current[singleTable.id]) {
          const values = await tableRefs?.current[singleTable?.id]?.getValues();
          const data = handleSingalTableRowData(singleTable, values, formDataOperateType);
          results.push(...(data || []));
        }
      }
    }
    return results;
  };

  const getButtonConfigValue = (buttonType: ButtonOperateType, key?: any) => {
    const buttonItem = formVersion.extConfig?.buttonConfigs?.find((item) => item.operateType === buttonType);
    console.log(key ? buttonItem?.[key] : buttonItem, 'style-----');
    let btnProps = {};
    if (key === 'buttonStyle') {
      switch (buttonItem?.buttonStyle) {
        case ButtonStyle.Primary:
          btnProps = { type: 'primary' };
          break;
        case ButtonStyle.PrimaryGhost:
          btnProps = { type: 'primary', ghost: true };
          break;
        case ButtonStyle.Danger:
          btnProps = { danger: true };
          break;
      }
      return btnProps;
    }

    return key ? buttonItem?.[key] : buttonItem;
  };

  const getCombinetags = () => {
    const combinetags = formVersion?.extConfig?.combineTagConfig?.fields;
    return handleTableData([], combinetags, initDataInfo);
  };

  // 获取提交参数
  const getSubmitParams = async (formDataOperateType?: FormDataOperateType) => {
    const values: FormValuesType = await form.validateFields();
    console.log(values, 'values------');
    const combineValues = await combineTagRef?.current?.getValues();

    // 基础分组行数据
    const commongroupRouDats = genRowDatas(values, formDataOperateType);
    // 组合标签行数据
    const combineRowDatas = handleCombineRowDatas(combineValues, formDataOperateType, commongroupRouDats);
    // 同组数据即数据拆分进来的数据是没有数据表格的
    const tableRowValues = isSameGroup ? [] : await handleAllTables(formDataOperateType);
    let groupAndCombineRows = combineRowDatas?.length > 0 ? combineRowDatas : commongroupRouDats;
    // if (combineRowDatas?.length > 0) {

    // }
    const rowDatas = [...groupAndCombineRows, ...tableRowValues];
    console.log(rowDatas, 'rowDatas--------------');
    // console.log(commongroupRouDats, combineRowDatas, formDataOperateType, tableRowValues, 'rowDatas--------')
    // return;
    let params: any = {
      postId: post?.postId,
      formId,
      formVersionId,
      rowDatas,
      preDataId: dataId, // 当前进入行数据的主数据单元的dataId
    };

    if (formVersion?.appType === AppType.SUBMIT) {
      if (isReturn) {
        // 退回-重新发起
        params = {
          ...params,
          formDataDraftId: bizId,
        };
      }
    } else if (formVersion?.appType === AppType.Approve) {
      // 审核表单-审核
      params = {
        ...params,
        formDataOperateType,
      };
    }

    return params;
  };

  // 提交
  const handleSubmit = async (formDataOperateType?: FormDataOperateType) => {
    if (isPreview) return;
    const params = await getSubmitParams(formDataOperateType);
    if (formVersion?.appType === AppType.SUBMIT) {
      // 发起表单-发起
      Modal.confirm({
        title: `确定发起该表单吗？`,
        onOk: async () => {
          try {
            await datasourceDataAPI.submitFormData(params);
            await message.success('操作成功', 1.5);
            bridge.close();
          } catch (error) {}
        },
      });
    } else if (formVersion?.appType === AppType.Approve || !isEmpty(dataGroupOption)) {
      // 审核表单-审核
      const actions = {
        [FormDataOperateType.Agree]: '同意',
        [FormDataOperateType.End]: '完结',
        [FormDataOperateType.Refuse]: '拒绝',
        [FormDataOperateType.Return]: '退回',
      };
      Modal.confirm({
        title: `确定${actions[formDataOperateType]}当前数据吗？`,
        onOk: async () => {
          try {
            await datasourceDataAPI.examineFormData(params);
            await message.success('操作成功', 1.5);
            bridge.close();
          } catch (error) {}
        },
      });
    }
  };

  // 预览
  const handlePreviewClick = async () => {
    const params = await getSubmitParams();
    const url = await datasourceDataAPI.renderWord(params);
    setPreviewModal({
      open: true,
      url,
    });
  };

  // 退回-取消申请
  const handleCancel = async () => {
    await pendingAPI.batchUpdatePending({
      bizType,
      bizIds: [bizId],
      refresh: true,
    });
    await message.success('操作成功', 1.5);
    bridge.close();
  };

  const renderItems = () => {
    // console.log(cFields, 'cFields------------');
    return cFields?.map((field, index) => {
      const uniqueId = getMetaTagUniqueId(field);
      const commonpiece = records?.[0]?.[uniqueId]?.tagOps && records[0]?.[uniqueId]?.tagOps?.length > 0;
      const ops = records?.[0]?.[uniqueId]?.tagOps;
      // console.log(records?.[0]?.[uniqueId]?.tagOps, records?.[0], uniqueId, ops, commonpiece, 'same----------------');
      const renderTag = () => {
        const tag = cTagsMap[uniqueId];
        if (!tag) {
          return null;
        }
        return (
          <>
            <Form.Item hidden name={[uniqueId, 'tagId']} initialValue={field.sourceId} />
            <Form.Item hidden name={[uniqueId, 'tagName']} initialValue={tag.name} />
            <Form.Item hidden name={[uniqueId, 'code']} initialValue={tag.code} />
            <Form.Item hidden name={[uniqueId, 'dataUnitId']} initialValue={field.dataUnitId} />
            {commonpiece ? (
              <CommonMetaDynamicItem useIn={UseIn.App} tag={tag} field={field} options={ops} />
            ) : (
              <MetaDynamicItem useIn={UseIn.App} tag={tag} field={field} />
            )}
          </>
        );
      };

      const renderCalcField = () => {
        return (
          <>
            <Form.Item
              label={
                <div className={styles.labelWrapper}>
                  <span>{field.fieldConfig?.fieldName}</span>
                </div>
              }
            >
              <span>{records?.[0]?.[field.fieldConfig?.fieldId] ?? '-'}</span>
            </Form.Item>
          </>
        );
      };

      return (
        <Col span={12} style={{ flexShrink: 0 }} key={index}>
          <div key={uniqueId} className={styles.formItemWrapper}>
            {field.sourceType === SourceType.Tag && renderTag()}
            {field.sourceType === SourceType.Field && renderCalcField()}
          </div>
        </Col>
      );
    });
  };

  return (
    <LayoutPage header={{ title: formVersion?.name, onClose: () => bridge.close() }} footer={false} loading={tablesLoading > 0 || loading}>
      <div className={styles.container} ref={containerRef}>
        <div className={styles.right} style={{ position: 'relative' }}>
          <Editor ref={editorRef} />
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              zIndex: isDargRef?.current ? 99 : -1,
            }}
          />
        </div>
        <div className={styles.left} style={{ position: 'relative', width: leftWidth }} ref={dragHandleRef}>
          <div style={{ position: 'absolute', top: '50%', left: 0, cursor: isDargRef?.current ? 'grabbing' : 'default' }}>
            <div className={styles.dragBtn} onMouseDown={handleMouseDown}>
              <span className={styles.btnLine}></span>
              <span className={styles.btnLine}></span>
            </div>
          </div>
          <div className={styles.leftHeader}>
            <span className={styles.groupTittle}>基础分组</span>
            <div style={{ display: 'flex', alignItems: 'center', columnGap: '10px' }}>
              {/* 默认有列表进入有 */}
              {dataId && (
                <Button
                  type="link"
                  style={{ padding: 0 }}
                  onClick={() => {
                    setOpenBizModal({
                      open: true,
                      type: BizModalEnums.HISTORYHANDLE,
                      data: null,
                    });
                  }}
                >
                  处理历史
                </Button>
              )}
              {/* 有列表进入子数据显示-同组数据 */}
              {isSameGroup && (
                <Button
                  type="link"
                  style={{ padding: 0 }}
                  onClick={() => {
                    setOpenBizModal({
                      open: true,
                      type: BizModalEnums.MAINDATAVIEW,
                      data: null,
                    });
                  }}
                >
                  主数据
                </Button>
              )}

              {/* 含有配置了动态入参的的标签显示 */}
              {!isEmpty(conditionMap) && (
                <Button type="link" style={{ padding: 0 }} onClick={() => {}}>
                  实时查询
                </Button>
              )}
              {/* 普通数据有列表进入可进行拆分，组合标签和已拆分主子数据不可拆分，不显示 */}
              {!isSameGroup && !isSamePiece && dataId && !allOutPut && (
                <Button
                  type="link"
                  style={{ padding: 0 }}
                  onClick={() => {
                    setOpenBizModal({
                      open: true,
                      type: BizModalEnums.SPLITDATA,
                    });
                  }}
                >
                  拆分审批
                </Button>
              )}
              <Button type="link" style={{ padding: 0 }} onClick={handlePreviewClick}>
                预览
              </Button>
            </div>
          </div>
          <div className={styles.leftContent}>
            <Form form={form} layout="vertical">
              {isReturn && (
                <div className={styles.returnWrapper}>
                  <div className={styles.returnTitle}>审批意见</div>
                  <div className={styles.returnContent}>{draftDetail?.approveMsg}</div>
                </div>
              )}
              <Row gutter={24}>{renderItems()}</Row>
            </Form>
            {!isEmpty(formVersion?.extConfig?.combineTagConfig) && (
              <>
                <div>组合标签</div>
                <div>
                  <DataTable
                    ref={combineTagRef}
                    setLoading={setLoading}
                    isPreview={isPreview}
                    combineConfig={formVersion?.extConfig?.combineTagConfig}
                    formVersion={formVersion}
                    post={post}
                    dataId={dataId}
                    getTableDatas={getCombineValues}
                    needDefault={formVersion && !dataId && !isReturn}
                    cTagsMap={cTagsMap}
                  />
                </div>
              </>
            )}

            {!isSameGroup && formVersion?.extConfig?.tableConfigs?.length > 0 && (
              <>
                <div className={classNames(styles.p10, styles.groupTittle)}>数据表格</div>
                <div>
                  {tableList?.length > 0 ? (
                    tableList.map((tabelItem, tableIndex) => (
                      <div key={tableIndex}>
                        <DataTable
                          ref={setTableRef(tabelItem.id)}
                          setLoading={setLoading}
                          isPreview={isPreview}
                          tableConfig={tabelItem}
                          formVersion={formVersion}
                          post={post}
                          dataId={dataId}
                          cTagsMap={cTagsMap}
                          bizId={bizId}
                          needDefault={formVersion && !dataId && !isReturn}
                        />
                      </div>
                    ))
                  ) : (
                    <Empty description="暂无数据" />
                  )}
                </div>
              </>
            )}
          </div>
          {/* !isPreview && */}
          {
            <div className={styles.leftFooter}>
              {/* {formVersion.appType === AppType.SUBMIT && ( */}
              <>
                {isReturn && (
                  <Button
                    onClick={() => {
                      Modal.confirm({
                        title: '确定取消申请该表单吗？',
                        onOk: () => {
                          handleCancel();
                        },
                      });
                    }}
                  >
                    取消申请
                  </Button>
                )}
                {!isEmpty(formVersion?.extConfig?.buttonConfigs?.find((btn) => btn?.operateType === ButtonOperateType.LAUNCH)) && (
                  <Button
                    {...(getButtonConfigValue(ButtonOperateType.LAUNCH, 'buttonStyle') || {})}
                    onClick={() => {
                      handleSubmit();
                    }}
                  >
                    发起
                  </Button>
                )}
              </>
              {/* )} */}
              {/* {(formVersion.appType === AppType.Approve || !isEmpty(dataGroupOption)) && ( */}
              <>
                {!isEmpty(formVersion?.extConfig?.buttonConfigs?.find((btn) => btn?.operateType === ButtonOperateType.END)) && (
                  <Button
                    size="small"
                    {...(getButtonConfigValue(ButtonOperateType.END, 'buttonStyle') || {})}
                    onClick={() => {
                      handleSubmit(FormDataOperateType.End);
                    }}
                  >
                    完结
                  </Button>
                )}
                {!isEmpty(formVersion?.extConfig?.buttonConfigs?.find((btn) => btn?.operateType === ButtonOperateType.RETURN)) && (
                  <Button
                    size="small"
                    {...(getButtonConfigValue(ButtonOperateType.RETURN, 'buttonStyle') || {})}
                    onClick={() => {
                      handleSubmit(FormDataOperateType.Return);
                    }}
                  >
                    退回
                  </Button>
                )}
                {!isEmpty(formVersion?.extConfig?.buttonConfigs?.find((btn) => btn?.operateType === ButtonOperateType.REFUSE)) && (
                  <Button
                    size="small"
                    {...(getButtonConfigValue(ButtonOperateType.REFUSE, 'buttonStyle') || {})}
                    onClick={() => {
                      handleSubmit(FormDataOperateType.Refuse);
                    }}
                  >
                    拒绝
                  </Button>
                )}
                {!isEmpty(formVersion?.extConfig?.buttonConfigs?.find((btn) => btn?.operateType === ButtonOperateType.AGREE)) && (
                  <Button
                    size="small"
                    {...(getButtonConfigValue(ButtonOperateType.AGREE, 'buttonStyle') || {})}
                    onClick={() => {
                      handleSubmit(FormDataOperateType.Agree);
                    }}
                  >
                    同意
                  </Button>
                )}
              </>
              {/* )} */}
            </div>
          }
        </div>
      </div>

      {/* 预览 */}
      {previewModal.open && (
        <EditorPreview
          fileUrl={previewModal.url}
          onClose={() => {
            setPreviewModal({ open: false });
          }}
        />
      )}
      {openBizModal.open && openBizModal.type === BizModalEnums.SPLITDATA && (
        <SplitDataModal
          // cDataUnits={cDataUnits}
          post={post}
          records={records}
          cFields={cFields}
          cTagsMap={cTagsMap}
          formVersion={formVersion}
          dataId={dataId}
          onCancel={() => {
            setOpenBizModal({ open: false, type: BizModalEnums.CLOSE });
          }}
          onOk={(values) => {
            console.log(values, 'values');
            setOpenBizModal({ open: false, type: BizModalEnums.CLOSE });
            bridge.close();
          }}
        />
      )}
      {openBizModal.open && openBizModal.type === BizModalEnums.HISTORYHANDLE && (
        <HandleDataHistory
          records={records}
          cFields={cFields}
          cTagsMap={cTagsMap}
          formVersion={formVersion}
          post={post}
          dataId={dataId}
          onCancel={() => {
            setOpenBizModal({ open: false, type: BizModalEnums.CLOSE });
          }}
          onOk={(values) => {
            setOpenBizModal({ open: false, type: BizModalEnums.CLOSE });
            // bridge.close();
            console.log(values, 'values');
          }}
        />
      )}
      {openBizModal.open && openBizModal.type === BizModalEnums.MAINDATAVIEW && (
        <MainDataView
          post={post}
          mainDataId={allDataIds?.[0]}
          cTagsMap={cTagsMap}
          formVersion={formVersion}
          onCancel={() => {
            setOpenBizModal({ open: false, type: BizModalEnums.CLOSE });
          }}
          onOk={(values) => {
            console.log(values, 'values');
          }}
        />
      )}
    </LayoutPage>
  );
};

export default AppForm;
