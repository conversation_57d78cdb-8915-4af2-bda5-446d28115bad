import { defineConfig } from '@umijs/max';
const packageJson = require('./package.json');

const genMfConfigs = (params: { name: string; remotes?: Array<{ baseUri; port }>; shared? }) => {
  const { remotes, shared = {}, name } = params;
  const config: any = {
    name,
    remoteHash: false,
    /**
     * 结合qiankun使用时配置library
     * name 须和 上面的mf.name 一致
     */
    library: {
      type: 'window',
      name,
    },
    remotes: remotes.map(({ baseUri, port }) => ({
      name: `micro${baseUri.charAt(0).toUpperCase()}${baseUri.slice(1)}`,
      keyResolver: `(() => {
        const match = window.location.search.match(/[?&]isDebug=([^&]*)/);
        const env = process.env.UMI_APP_ENV;
        const isDebug = process.env.UMI_APP_ENV !== 'prod' && (match ? !!decodeURIComponent(match[1]) : false);
        if (isDebug) {
          const debugConfig = JSON.parse(window.sessionStorage.getItem('@@micro-app-entry-config-key') || '{}');

          return debugConfig['${baseUri}'] || process.env.UMI_APP_ENV
        }
        return process.env.UMI_APP_ENV
      })()`,
      entries: {
        local: `http://localhost:${port}/remote.js`,
        dev: `https://dev-micro-app.gongwuyun.net/${baseUri}/remote.js`,
        test: `https://micro-app.gongwuyun.net/${baseUri}/remote.js`,
        pre: `https://micro-app.gongwuyun.cn/${baseUri}/remote.js`,
        prod: `https://micro-app.gongwuyun.com/${baseUri}/remote.js`,
      },
    })),
    shared: {
      ...shared,
      react: {
        singleton: true,
      },
      'react-dom': {
        singleton: true,
      },
      'react/jsx-runtime': {
        singleton: true,
      },
      'react/jsx-dev-runtime': {
        singleton: true,
      },
    },
  };
  return config;
};

export default defineConfig({
  hash: true,
  styleLoader: {},
  jsMinifierOptions: {
    target: ['chrome80', 'es2020'],
  },
  antd: {
    theme: {
      token: {
        borderRadius: 4,
        colorText: '#1d2129',
      },
      components: {
        Form: {
          itemMarginBottom: 10,
          verticalLabelPadding: 2,
        },
      },
    },
  },
  // 需求用数据流才可使用useModel与主应用通信
  model: {},
  base: '/datasource/',
  // 配置publicPath时，runtimePublicPath为必配项
  runtimePublicPath: {},
  // 入口文件加载路径添加前缀
  publicPath: process.env.NODE_ENV === 'production' ? '/datasource/' : '/',
  // publicPath: '/datasource/',
  qiankun: {
    // master: {},
    slave: {},
  },
  proxy: {
    // '/api/gwy-datasource-data': {
    //   // target: 'https://tmp-www.gongwuyun.net',
    //   target: 'http://**********:11008', // 世超 ip
    //   changeOrigin: true,
    //   pathRewrite: {
    //     '^/api/gwy-datasource-data': '',
    //   },
    // },
    // '/api/gwy-datasource': {
    //   // target: 'https://tmp-www.gongwuyun.net',
    //   target: 'http://**********:11005', // 世超 ip
    //   changeOrigin: true,
    //   pathRewrite: {
    //     '^/api/gwy-datasource': '',
    //   },
    // },
    '/api': {
      target: 'https://dev-www.gongwuyun.net',
      // target: 'http://*********:11003', // 祖威 ip
      changeOrigin: true,
      // pathRewrite: {
      // '^/api': '',
      // },
    },
  },
  npmClient: 'yarn',
  conventionRoutes: {
    exclude: [/\/components/],
  },
  /**
   * 详细配置参见 [https://umijs.org/docs/max/mf]
   *
   * 结合qiankun微前端使用时remotes最少要配一个，不然会出现一些问题
   * 例如：[Uncaught Error: Shared module is not available for eager consumption]
   * 默认要配置成引用自己，如需引用其他项目，则可删除引用自己的配置
   *
   */
  mf: genMfConfigs({ name: 'microDatasource', remotes: [{ baseUri: 'datasource', port: 8003 }], shared: packageJson.dependencies }),
  headScripts: [
    {
      type: 'text/javascript',
      src: `https://api.map.baidu.com/api?type=webgl&v=1.0&ak=4ZQKYAaGU1N3tCPRUAu8wKSE7aBkGOgf`,
    },
  ],
});
