import { TextDTOKey } from '@/components/metadata-dialog/metadata-config/text-config';
import { textCharTypeOptions, TextInputType } from '@/const/metadata';
import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { Form, InputNumber, Radio, Select } from '@gwy/components-web';
import { useEffect, useMemo } from 'react';
import { WidgetType } from '../const';
import InputEnumConfig from './input-enum-config';

type Props = {
  tag?: DataUnitTagVO;
  field?: FormField;
};

const TextManualPropertySetting = ({ tag, field }: Props) => {
  const form = Form.useFormInstance();

  const { maxLength, allowedCharacters } = tag?.tagMetaDataConfig?.metaDataTextDTO || {};

  const textCharTypeFilteredOptions = useMemo(() => {
    return textCharTypeOptions.filter((item) => {
      if (Array.isArray(allowedCharacters) && allowedCharacters.length > 0) {
        return allowedCharacters.includes(item.value);
      }
      return true;
    });
  }, [allowedCharacters]);

  const inputType = Form.useWatch([TextDTOKey, 'inputType'], form);
  useEffect(() => {
    form.setFieldsValue(field.config);
  }, []);

  return (
    <div>
      <Form.Item label="输入方式" name={[TextDTOKey, 'inputType']} initialValue={TextInputType.manual}>
        <Radio.Group
          options={[
            { label: '输入框', value: TextInputType.manual },
            { label: '选择器', value: TextInputType.enum },
          ]}
        />
      </Form.Item>
      {inputType === TextInputType.enum && (
        <Form.Item
          name={[TextDTOKey, 'enumList']}
          rules={[
            {
              validator: async (rule, value) => {
                if (!value || value.length === 0) {
                  return Promise.reject('请添加选项');
                }
                if (value.some((ite) => !ite.item)) {
                  return Promise.reject('请填写完整选项');
                }
                if (value.length !== new Set(value.map((v) => v.item)).size) {
                  return Promise.reject('存在重复选项');
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <InputEnumConfig />
        </Form.Item>
      )}
      {inputType === TextInputType.manual && (
        <>
          <Form.Item label="文本控件" name={['widgetType']}>
            <Radio.Group>
              <Radio value={WidgetType.LONG}>长文本</Radio>
              <Radio value={WidgetType.SHORT}>短文本</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item label="文本包含" name={[TextDTOKey, 'allowedCharacters']} rules={[{ required: true, message: '请选择' }]}>
            <Select options={textCharTypeFilteredOptions} mode="multiple" maxTagCount="responsive" />
          </Form.Item>
          <Form.Item
            label="字数限制"
            name={[TextDTOKey, 'maxLength']}
            rules={[
              { required: true, message: '此为必填项，不可为空' },
              { type: 'number', min: 1, max: maxLength, message: `字数限制在1-${maxLength}字数之间` },
            ]}
          >
            <InputNumber style={{ width: '100%' }} placeholder="请输入字数限制" />
          </Form.Item>
        </>
      )}
    </div>
  );
};

export default TextManualPropertySetting;
