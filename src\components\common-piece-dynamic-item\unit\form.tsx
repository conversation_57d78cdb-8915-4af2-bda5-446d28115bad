import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { Select } from '@gwy/components-web';
import { memo } from 'react';
import { UseIn } from '../const';

export type UnitMetaProps = {
  useIn?: UseIn;
  tag?: DataUnitTagVO;
  field?: FormField;
  value?: any;
  onChange?: (value) => void;
  styles?: React.CSSProperties;
  isPreview?: boolean;
  options?: any;
  label?: React.ReactNode;
};

const UnitMeta = memo<UnitMetaProps>(({ useIn, tag, field, value, onChange, styles, isPreview, options }) => {
  const { raw_value, raw_unit } = value || {};
  const { config, readonly, placeholder } = field || {};
  const { configList } = config?.metaDataUnitDTO || {};
  const onValueVhange = (data: { raw_value?; raw_unit? }) => {
    typeof onChange === 'function' &&
      onChange({
        raw_value,
        raw_unit,
        // ...data,
      });
  };

  return (
    <Select
      value={`${raw_value}${raw_unit}`}
      onChange={onChange}
      options={options?.map((option) => ({
        value: `${option?.raw_value}${option?.raw_unit}`,
        label: `${option?.raw_value}${option?.raw_unit}`,
      }))}
    />
  );
});

export default UnitMeta;
