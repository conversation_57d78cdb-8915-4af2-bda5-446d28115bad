import { AppDesignerContext } from '@/pages/app-designer/context';
import { FormField, SourceType } from '@/types/form-field';
import { Location } from '@/types/statistic-item';
import { getFormFieldDisplayName, getMetaTagUniqueId } from '@/utils/metadata';
import { TreeSelect } from '@gwy/components-web';
import { groupBy, map } from 'lodash-es';
import { useContext } from 'react';

type Props = {
  formFields?: FormField[];
  value?: Location;
  onChange?: (value: Location) => void;
};

const FormFieldSelect = ({ formFields = [], value, onChange }: Props) => {
  const { tagsMap } = useContext(AppDesignerContext);

  const tagOptions = map(
    groupBy(
      formFields?.filter((f) => f.sourceType === SourceType.Tag),
      'dataUnitId',
    ),
    (group) => {
      const tag = tagsMap[getMetaTagUniqueId(group[0])];
      return {
        title: tag.$$dataUnit?.name,
        value: tag.$$dataUnit?.dataUnitId,
        selectable: false,
        children: group.map((tag) => ({
          ...tag,
          title: getFormFieldDisplayName(tag, tagsMap),
          value: getMetaTagUniqueId(tag),
        })),
      };
    },
  );

  const fieldOptions = {
    title: '字段',
    value: 'field',
    selectable: false,
    children: formFields
      ?.filter((f) => f.sourceType === SourceType.Field)
      .map((field) => ({
        ...field,
        title: getFormFieldDisplayName(field, tagsMap),
        value: getMetaTagUniqueId(field),
      })),
  };

  const options = [...tagOptions, fieldOptions];

  return (
    <TreeSelect
      showSearch
      treeNodeFilterProp="title"
      style={{ width: '100%' }}
      placeholder="请选择"
      treeDefaultExpandAll
      value={value ? (value.sourceType === SourceType.Field ? value.sourceId : `${value.dataUnitId}_${value.sourceId}`) : undefined}
      treeData={options}
      onChange={(v, label, extra: any) => {
        if (!v) {
          onChange?.(undefined);
          return;
        }
        const option: FormField = extra.triggerNode.props;

        if (onChange) {
          onChange({
            sourceType: option.sourceType,
            dataUnitId: option.sourceType === SourceType.Field ? undefined : option.dataUnitId,
            sourceId: option.sourceType === SourceType.Field ? option.fieldConfig.fieldId : option.sourceId,
          });
        }
      }}
    />
  );
};

export default FormFieldSelect;
