import DataQuerySetting, { getExtConfig } from '@/components/data-query-setting';
import { AppType } from '@/pages/app-designer/const';
import { AppDesignerContext } from '@/pages/app-designer/context';
import { SourceType } from '@/types/form-field';
import { DeleteOutlined, EditOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { Button, Form, Select } from 'antd';
import { produce } from 'immer';
import { useContext, useEffect, useMemo, useState } from 'react';
import { LeftPanelTabType } from '../../../left-panel';
import styles from './index.less';

type DataUnitSelectProps = {
  type?: 'baseGroup' | 'table';
};

const DataUnitSelect = ({ type = 'baseGroup' }: DataUnitSelectProps) => {
  const {
    groupFlag,
    aggregationMainDataUnitId,
    aggregationMainDataUnits,
    appType,
    availableDataUnits,
    configuredDataUnits,
    setConfiguredDataUnits,
    setConfiguredFields,
    combineTagConfig,
    setCombineTagConfig,
    allConfiguredDataUnitsWithTags: configuredDataUnitsWithTags,
    tableList,
    setTableList,
    selectedState,
    calcFields,
    setLeftPanelTabType,
  } = useContext(AppDesignerContext);
  const [form] = Form.useForm();

  const [openFilter, setOpenFilter] = useState({
    open: false,
    dataUnit: null,
  });
  const dataUnitsOptions = (
    groupFlag
      ? [
          {
            dataUnitId: aggregationMainDataUnitId,
            name: aggregationMainDataUnits.find((item) => item.dataUnitId === aggregationMainDataUnitId)?.name,
          },
          ...availableDataUnits,
        ]
      : availableDataUnits
  ).map((item) => ({
    label: item.name,
    value: item.dataUnitId,
  }));

  // 基础分组-数据单元切换时，只保留当前数据单元的标签
  useEffect(() => {
    setConfiguredFields((pre) =>
      pre.filter((f) => {
        if (f.sourceType === SourceType.Tag) {
          return configuredDataUnits?.some((dataUnit) => dataUnit.dataUnitId === f.dataUnitId);
        }
        return true;
      }),
    );
    if (combineTagConfig) {
      setCombineTagConfig((pre) => {
        return {
          ...pre,
          fields: pre?.fields?.filter((f) => {
            if (f.sourceType === SourceType.Tag) {
              return configuredDataUnits?.some((dataUnit) => dataUnit.dataUnitId === f.dataUnitId);
            }
            return true;
          }),
        };
      });
    }
  }, [configuredDataUnits]);

  // 数据表格-数据单元切换时，只保留当前数据单元的标签
  const table = tableList?.find((t) => t.id === selectedState.tableId);
  useEffect(() => {
    if (type === 'baseGroup') {
      return;
    }

    setTableList(
      produce(tableList, (draft) => {
        const table = draft.find((t) => t.id === selectedState.tableId);
        if (table) {
          table.fields = table.fields?.filter((f) => {
            if (f.sourceType === SourceType.Tag) {
              return table?.dataUnits?.some((dataUnit) => dataUnit.dataUnitId === f.dataUnitId);
            }
            return true;
          });
        }
      }),
    );
  }, [type, selectedState.tableId, table?.dataUnits]);

  const _configuredDataUnits = useMemo(() => {
    if (type === 'baseGroup') {
      return configuredDataUnits;
    } else if (type === 'table') {
      return tableList?.find((t) => t.id === selectedState.tableId)?.dataUnits || [];
    }
  }, [configuredDataUnits, tableList, selectedState.tableId, type]);

  // 当前数据单元的标签，变量可关联的标签
  const [unitIdtoTags, canRelateTags] = useMemo(() => {
    let tags = [];
    let canRelateTags = [];
    if (openFilter?.dataUnit?.dataUnitId) {
      tags = configuredDataUnitsWithTags
        ?.find((dataUnit) => dataUnit.dataUnitId === openFilter.dataUnit.dataUnitId)
        ?.tagList.map((tag) => ({
          ...tag,
          belongDataUnitId: openFilter.dataUnit.dataUnitId,
          belongDataUnitName: openFilter.dataUnit.name,
        }));
    }
    // 如果是主数据单元，则是当前标签列表
    // 如果是副数据单元，则当前主数列和副数列的标签，具体类型在内部组件过滤
    if (openFilter?.dataUnit?.dataUnitId === configuredDataUnits[0]?.dataUnitId) {
      canRelateTags = tags;
    } else {
      canRelateTags = configuredDataUnitsWithTags
        ?.find((dataUnit) => dataUnit.dataUnitId === configuredDataUnits[0]?.dataUnitId)
        ?.tagList.map((tag) => ({
          ...tag,
          belongDataUnitId: configuredDataUnits[0]?.dataUnitId,
          belongDataUnitName: configuredDataUnits[0]?.name,
        }))
        .concat(tags);
    }

    return [tags, canRelateTags];
  }, [openFilter.dataUnit, configuredDataUnitsWithTags]);

  return (
    <div className={styles.container}>
      {_configuredDataUnits.map((dataUnit, i) => {
        const isMain = i === 0;
        return (
          <div key={i} className={styles.item}>
            <Form.Item layout="vertical" label={isMain ? '主数据单元' : '副数据单元'} style={{ marginBottom: 0 }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <Select
                  disabled={groupFlag}
                  style={{ flexGrow: 1 }}
                  allowClear
                  showSearch
                  optionFilterProp="label"
                  options={dataUnitsOptions.map((option) => {
                    let cDataUnitsIds = [];
                    if (type === 'baseGroup') {
                      cDataUnitsIds = configuredDataUnits.map((item) => item.dataUnitId);
                    } else if (type === 'table') {
                      cDataUnitsIds = tableList?.find((t) => t.id === selectedState.tableId)?.dataUnits?.map((item) => item.dataUnitId);
                    }
                    return {
                      ...option,
                      disabled: cDataUnitsIds?.includes(option.value) && option.value !== dataUnit.dataUnitId,
                    };
                  })}
                  value={dataUnit.dataUnitId}
                  onChange={(value) => {
                    if (type === 'baseGroup') {
                      const dataUnit = availableDataUnits.find((item) => item.dataUnitId === value);
                      const cDataUnitsNew = produce(configuredDataUnits, (draft) => {
                        draft[i] = {
                          ...dataUnit,
                          extConfig: getExtConfig(),
                          conditionGroups: [],
                        };
                      });
                      setConfiguredDataUnits(cDataUnitsNew);
                    } else if (type === 'table') {
                      const dataUnit = availableDataUnits.find((item) => item.dataUnitId === value);
                      const newTableList = produce(tableList, (draft) => {
                        const table = draft.find((t) => t.id === selectedState.tableId);
                        table.dataUnits[i] = {
                          ...dataUnit,
                          extConfig: getExtConfig(),
                          conditionGroups: [],
                        };
                      });
                      setTableList(newTableList);
                    }

                    setLeftPanelTabType(LeftPanelTabType.components);
                  }}
                  placeholder="请选择"
                />
                {!isMain && (
                  <DeleteOutlined
                    className={styles.deleteIcon}
                    onClick={() => {
                      if (type === 'baseGroup') {
                        const cDataUnitsNew = produce(configuredDataUnits, (draft) => {
                          draft.splice(i, 1);
                        });
                        setConfiguredDataUnits(cDataUnitsNew);
                      } else if (type === 'table') {
                        const newTableList = produce(tableList, (draft) => {
                          const table = draft.find((t) => t.id === selectedState.tableId);
                          if (!table.dataUnits) {
                            table.dataUnits = [];
                          }
                          table.dataUnits.splice(i, 1);
                        });
                        setTableList(newTableList);
                      }
                    }}
                  />
                )}
              </div>
            </Form.Item>
            {!groupFlag && appType === AppType.Approve && (
              <Form.Item label="配置规则" style={{ marginBottom: 0 }}>
                <Button
                  type="link"
                  icon={<EditOutlined />}
                  style={{ padding: 0 }}
                  onClick={() =>
                    setOpenFilter((prev) => ({
                      ...prev,
                      open: true,
                      dataUnit,
                    }))
                  }
                >
                  {dataUnit?.conditionGroups?.length > 0 ? `${dataUnit?.conditionGroups?.length}个条件` : '数据查询规则'}
                </Button>
              </Form.Item>
            )}
          </div>
        );
      })}

      {!groupFlag && (
        <Button
          color="primary"
          variant="dashed"
          block
          icon={<PlusCircleOutlined />}
          onClick={() => {
            if (type === 'baseGroup') {
              setConfiguredDataUnits((pre) => [...pre, {}]);
            } else if (type === 'table') {
              const newTableList = produce(tableList, (draft) => {
                const table = draft?.find((t) => t.id === selectedState.tableId);
                table.dataUnits = [...(table.dataUnits || []), {}];
              });
              setTableList(newTableList);
            }
          }}
        >
          添加数据单元
        </Button>
      )}
      {openFilter.open && (
        <DataQuerySetting
          isMainDataUnit={openFilter.dataUnit.dataUnitId === _configuredDataUnits[0]?.dataUnitId}
          dataUnitName={openFilter.dataUnit?.name}
          dataUnitId={openFilter.dataUnit?.dataUnitId}
          conditionGroups={openFilter.dataUnit?.conditionGroups}
          extConfig={openFilter.dataUnit?.extConfig}
          tags={unitIdtoTags}
          canRelateTags={canRelateTags}
          calcFields={calcFields}
          onOk={(values) => {
            console.log(values, 'values-----');
            setOpenFilter({
              open: false,
              dataUnit: null,
            });
            if (type === 'baseGroup') {
              setConfiguredDataUnits(
                configuredDataUnits.map((item) => {
                  if (item.dataUnitId === openFilter.dataUnit.dataUnitId) {
                    return {
                      ...item,
                      ...values,
                    };
                  }
                  return item;
                }),
              );
            } else if (type === 'table') {
              const newTableList = produce(tableList, (draft) => {
                const table = draft?.find((t) => t.id === selectedState.tableId);
                table.dataUnits = table.dataUnits.map((item) => {
                  if (item.dataUnitId === openFilter.dataUnit.dataUnitId) {
                    return {
                      ...item,
                      ...values,
                    };
                  }
                  return item;
                });
              });
              setTableList(newTableList);
            }
          }}
          onCancel={() => {
            setOpenFilter({
              open: false,
              dataUnit: null,
            });
          }}
        />
      )}
    </div>
  );
};

export default DataUnitSelect;
